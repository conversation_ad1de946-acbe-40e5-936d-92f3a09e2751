#!/usr/bin/env python3
"""
测试修正后的今日菜单API
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, AdministrativeArea
from datetime import date
import json

def test_fixed_api():
    app = create_app()
    
    with app.app_context():
        print("=== 测试修正后的今日菜单API ===\n")
        
        # 查找城南小学的用户
        school = AdministrativeArea.query.filter(
            AdministrativeArea.name.like('%城南小学%')
        ).first()
        
        if not school:
            print("未找到城南小学")
            return
            
        user = User.query.filter_by(area_id=school.id).first()
        if not user:
            print("城南小学没有用户")
            return
            
        print(f"学校: {school.name} (ID: {school.id})")
        print(f"用户: {user.username} (ID: {user.id})")
        print(f"测试日期: {date.today()}")
        
        # 模拟登录状态，直接调用API逻辑
        from app.routes.dashboard_api import api_v2_today_menu
        from flask_login import login_user
        
        # 设置当前用户
        with app.test_request_context():
            login_user(user)
            
            # 直接调用API函数
            try:
                response = api_v2_today_menu()
                
                if hasattr(response, 'get_json'):
                    data = response.get_json()
                else:
                    data = response
                    
                print("\n修正后的API返回结果:")
                print(json.dumps(data, ensure_ascii=False, indent=2))
                
                if data.get('success') and data.get('data'):
                    menu_data = data['data']
                    print("\n解析后的菜单数据:")
                    
                    for meal_type in ['早餐', '午餐', '晚餐']:
                        if meal_type in menu_data:
                            meal_info = menu_data[meal_type]
                            recipes = meal_info.get('recipes', [])
                            status = meal_info.get('status', '未知')
                            
                            print(f"\n{meal_type} (状态: {status}):")
                            if recipes:
                                for i, recipe in enumerate(recipes, 1):
                                    name = recipe.get('name', '未知菜品')
                                    quantity = recipe.get('quantity', '')
                                    print(f"  {i}. {name} {quantity}")
                                print(f"  共 {len(recipes)} 道菜品")
                                
                                # 检查是否还有重复
                                recipe_names = [r.get('name') for r in recipes]
                                unique_names = set(recipe_names)
                                if len(recipe_names) != len(unique_names):
                                    print(f"  ⚠️ 发现重复菜品!")
                                else:
                                    print(f"  ✅ 无重复菜品")
                            else:
                                print("  无菜谱")
                
            except Exception as e:
                print(f"API调用失败: {str(e)}")
                import traceback
                traceback.print_exc()

if __name__ == '__main__':
    test_fixed_api()
