2025-06-06 19:40:28,603 ERROR: 批量操作失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT supplier_products.id AS supplier_products_id, supplier_products.supplier_id AS supplier_products_supplier_id, supplier_products.ingredient_id AS supplier_products_ingredient_id, supplier_products.product_code AS supplier_products_product_code, supplier_products.product_name AS supplier_products_product_name, supplier_products.model_number AS supplier_products_model_number, supplier_products.specification AS supplier_products_specification, supplier_products.price AS supplier_products_price, supplier_products.quality_cert AS supplier_products_quality_cert, supplier_products.quality_standard AS supplier_products_quality_standard, supplier_products.product_image AS supplier_products_product_image, supplier_products.lead_time AS supplier_products_lead_time, supplier_products.min_order_quantity AS supplier_products_min_order_quantity, supplier_products.is_available AS supplier_products_is_available, supplier_products.shelf_status AS supplier_products_shelf_status, supplier_products.shelf_time AS supplier_products_shelf_time, supplier_products.shelf_operator_id AS supplier_products_shelf_operator_id, supplier_products.description AS supplier_products_description, supplier_products.batch_id AS supplier_products_batch_id, supplier_products.created_at AS supplier_products_created_at, supplier_products.updated_at AS supplier_products_updated_at 
FROM supplier_products 
WHERE supplier_products.id IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('91', '90', '89', '88', '87', '86', '85', '79', '78', '77')]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\supplier_product.py:760]
2025-06-06 19:41:20,320 ERROR: 批量操作失败: (pyodbc.Error) ('HY104', '[HY104] [Microsoft][ODBC SQL Server Driver]无效的精度值 (0) (SQLBindParameter)')
[SQL: SELECT supplier_products.id AS supplier_products_id, supplier_products.supplier_id AS supplier_products_supplier_id, supplier_products.ingredient_id AS supplier_products_ingredient_id, supplier_products.product_code AS supplier_products_product_code, supplier_products.product_name AS supplier_products_product_name, supplier_products.model_number AS supplier_products_model_number, supplier_products.specification AS supplier_products_specification, supplier_products.price AS supplier_products_price, supplier_products.quality_cert AS supplier_products_quality_cert, supplier_products.quality_standard AS supplier_products_quality_standard, supplier_products.product_image AS supplier_products_product_image, supplier_products.lead_time AS supplier_products_lead_time, supplier_products.min_order_quantity AS supplier_products_min_order_quantity, supplier_products.is_available AS supplier_products_is_available, supplier_products.shelf_status AS supplier_products_shelf_status, supplier_products.shelf_time AS supplier_products_shelf_time, supplier_products.shelf_operator_id AS supplier_products_shelf_operator_id, supplier_products.description AS supplier_products_description, supplier_products.batch_id AS supplier_products_batch_id, supplier_products.created_at AS supplier_products_created_at, supplier_products.updated_at AS supplier_products_updated_at 
FROM supplier_products 
WHERE supplier_products.id IN (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)]
[parameters: ('91', '90', '89', '88', '87', '86', '85', '84', '83', '82')]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\supplier_product.py:760]
2025-06-06 19:41:42,246 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 19:42:40,920 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 19:44:00,096 INFO: 查询菜谱：日期=2025-06-06, 星期=4(0=周一), day_of_week=5, 餐次=午餐, 区域ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-06 19:44:00,103 INFO: 找到 1 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-06 19:44:00,108 INFO: 匹配条件的食谱有 1 个 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:346]
2025-06-06 19:44:00,123 INFO:   - 食谱: 米饭（朝阳区实验中学版） [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:361]
2025-06-06 19:44:00,128 INFO: 食材一致性分析完成: 匹配率=0.0%, 缺失=1, 多余=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:553]
2025-06-06 19:44:57,324 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 19:44:57,344 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 19:45:00,471 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 19:45:00,478 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 19:45:31,801 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 19:46:07,264 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 19:46:17,514 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 19:46:17,521 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 19:57:27,699 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 19:59:43,537 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 19:59:43,558 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:02:16,698 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:02:16,702 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:03:22,353 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:03:22,360 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:03:51,167 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:03:51,174 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:14:26,998 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:14:27,004 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:15:06,487 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:15:06,491 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:16:41,093 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:16:41,098 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:16:56,893 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:16:56,896 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:16:58,493 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:16:58,497 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:17:00,598 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:17:00,602 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:17:39,700 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:17:39,703 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:21:41,989 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
