<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频集成测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h2>视频集成测试页面</h2>
        <p class="text-muted">测试后台视频内容在前台引导页面中的显示效果</p>
        
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>测试步骤</h5>
                    </div>
                    <div class="card-body">
                        <ol>
                            <li>确保数据库中有视频记录</li>
                            <li>点击下方按钮测试API</li>
                            <li>检查视频显示效果</li>
                            <li>测试视频播放功能</li>
                        </ol>
                    </div>
                </div>
            </div>
            
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h5>API测试</h5>
                    </div>
                    <div class="card-body">
                        <button class="btn btn-primary btn-block mb-2" onclick="testVideoAPI('daily_management')">
                            测试日常管理视频API
                        </button>
                        <button class="btn btn-success btn-block mb-2" onclick="testVideoAPI('suppliers')">
                            测试供应商管理视频API
                        </button>
                        <button class="btn btn-info btn-block mb-2" onclick="testVideoAPI('weekly_menu')">
                            测试周菜单视频API
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>API响应结果</h5>
            </div>
            <div class="card-body">
                <pre id="apiResult" class="bg-light p-3" style="max-height: 400px; overflow-y: auto;">点击上方按钮测试API...</pre>
            </div>
        </div>
        
        <div class="card mt-4">
            <div class="card-header">
                <h5>视频显示测试</h5>
            </div>
            <div class="card-body">
                <div id="videoTestArea">
                    <p class="text-muted">选择一个步骤测试视频显示效果</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@4.6.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        function testVideoAPI(stepName) {
            $('#apiResult').text('正在请求API...');
            
            $.ajax({
                url: `/api/guide/videos/${stepName}`,
                type: 'GET',
                success: function(data) {
                    $('#apiResult').text(JSON.stringify(data, null, 2));
                    
                    // 显示视频测试区域
                    if (data.success && data.videos && data.videos.length > 0) {
                        renderTestVideos(stepName, data);
                    } else {
                        $('#videoTestArea').html(`
                            <div class="alert alert-warning">
                                <h6>步骤 "${stepName}" 暂无视频</h6>
                                <p>请在后台管理中为该步骤上传视频</p>
                            </div>
                        `);
                    }
                },
                error: function(xhr, status, error) {
                    $('#apiResult').text(`API请求失败: ${error}\n状态码: ${xhr.status}\n响应: ${xhr.responseText}`);
                    $('#videoTestArea').html(`
                        <div class="alert alert-danger">
                            <h6>API请求失败</h6>
                            <p>错误: ${error}</p>
                        </div>
                    `);
                }
            });
        }
        
        function renderTestVideos(stepName, data) {
            let html = `
                <div class="alert alert-success">
                    <h6>步骤 "${stepName}" 的视频 (${data.videos.length}个)</h6>
                    <p>数据来源: ${data.source === 'database' ? '数据库' : '静态配置'}</p>
                </div>
                <div class="row">
            `;
            
            data.videos.forEach(function(video, index) {
                html += `
                    <div class="col-md-4 mb-3">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">${video.name}</h6>
                                <p class="card-text small">${video.description || '无描述'}</p>
                                <p class="text-info small">
                                    <i class="fas fa-clock mr-1"></i>${video.duration || '未知'}
                                    ${data.source === 'database' ? '<span class="badge badge-success ml-2">数据库</span>' : '<span class="badge badge-info ml-2">静态</span>'}
                                </p>
                                <button class="btn btn-primary btn-sm" onclick="playTestVideo('${video.url}', '${video.name}')">
                                    <i class="fas fa-play mr-1"></i>播放
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            $('#videoTestArea').html(html);
        }
        
        function playTestVideo(videoUrl, videoName) {
            const modalHtml = `
                <div class="modal fade" id="testVideoModal" tabindex="-1" role="dialog">
                    <div class="modal-dialog modal-lg" role="document">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="fas fa-play-circle mr-2"></i>${videoName}
                                </h5>
                                <button type="button" class="close" data-dismiss="modal">
                                    <span>&times;</span>
                                </button>
                            </div>
                            <div class="modal-body p-0">
                                <video controls width="100%" height="400">
                                    <source src="${videoUrl}" type="video/mp4">
                                    您的浏览器不支持视频播放。
                                </video>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-dismiss="modal">
                                    关闭
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            // 移除已存在的模态框
            $('#testVideoModal').remove();
            
            // 添加新的模态框
            $('body').append(modalHtml);
            $('#testVideoModal').modal('show');
            
            // 模态框关闭时清理
            $('#testVideoModal').on('hidden.bs.modal', function() {
                $(this).remove();
            });
        }
    </script>
</body>
</html>
