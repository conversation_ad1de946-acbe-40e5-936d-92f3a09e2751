#!/usr/bin/env python3
"""
专门查询城南小学周五的菜单数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import WeeklyMenu, WeeklyMenuRecipe
from datetime import date
from sqlalchemy import text

def debug_friday_menu():
    app = create_app()
    
    with app.app_context():
        print("=== 城南小学周五菜单详细调试 ===\n")
        
        area_id = 22  # 城南小学
        today = date.today()
        day_of_week = today.weekday() + 1  # 今天是周五，应该是5
        
        print(f"今天: {today}")
        print(f"day_of_week: {day_of_week}")
        print(f"学校ID: {area_id}\n")
        
        # 1. 查找当前周的菜单
        current_weekly_menu = WeeklyMenu.query.filter(
            WeeklyMenu.area_id == area_id,
            WeeklyMenu.week_start <= today,
            WeeklyMenu.week_end >= today
        ).first()
        
        if current_weekly_menu:
            print(f"当前周菜单: ID={current_weekly_menu.id}")
            print(f"状态: {current_weekly_menu.status}")
            print(f"周期: {current_weekly_menu.week_start} ~ {current_weekly_menu.week_end}\n")
            
            # 2. 查询周五的所有菜谱
            friday_recipes = WeeklyMenuRecipe.query.filter_by(
                weekly_menu_id=current_weekly_menu.id,
                day_of_week=day_of_week
            ).all()
            
            print(f"周五(day_of_week={day_of_week})的菜谱数量: {len(friday_recipes)}")
            
            if friday_recipes:
                print("周五菜谱详情:")
                for recipe in friday_recipes:
                    print(f"  ID: {recipe.id}, 餐次: {recipe.meal_type}, 菜谱: {recipe.recipe_name}")
            else:
                print("周五没有菜谱数据")
                
                # 检查其他day_of_week的数据
                print("\n检查所有day_of_week的数据:")
                all_recipes = WeeklyMenuRecipe.query.filter_by(
                    weekly_menu_id=current_weekly_menu.id
                ).order_by(WeeklyMenuRecipe.day_of_week).all()
                
                for recipe in all_recipes:
                    print(f"  day_of_week={recipe.day_of_week}, 餐次: {recipe.meal_type}, 菜谱: {recipe.recipe_name}")
        
        # 3. 使用API中的SQL查询逻辑
        print(f"\n=== 使用API的SQL查询逻辑 ===")
        
        for meal_type in ['早餐', '午餐', '晚餐']:
            print(f"\n查询{meal_type}:")
            
            # 这是API中使用的SQL
            menu_sql = text("""
            SELECT wmr.recipe_name
            FROM weekly_menu_recipes wmr
            INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
            WHERE wmr.day_of_week = :day_of_week
            AND wmr.meal_type = :meal_type
            AND wm.area_id = :area_id
            AND wm.status IN ('已发布', '计划中')
            ORDER BY wmr.id
            """)
            
            try:
                result = db.session.execute(menu_sql, {
                    'day_of_week': day_of_week,
                    'meal_type': meal_type,
                    'area_id': area_id
                })
                
                recipes = []
                for row in result:
                    recipes.append(row.recipe_name)
                    print(f"  找到菜谱: {row.recipe_name}")
                
                if not recipes:
                    print(f"  {meal_type}没有菜谱")
                else:
                    print(f"  {meal_type}共{len(recipes)}道菜")
                    
            except Exception as e:
                print(f"  查询{meal_type}失败: {str(e)}")
        
        # 4. 检查是否有其他学校的数据干扰
        print(f"\n=== 检查数据完整性 ===")
        
        # 查询所有学校的周五菜单
        all_friday_sql = text("""
        SELECT wm.area_id, wmr.meal_type, wmr.recipe_name, aa.name as school_name
        FROM weekly_menu_recipes wmr
        INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
        LEFT JOIN administrative_areas aa ON wm.area_id = aa.id
        WHERE wmr.day_of_week = :day_of_week
        AND wm.status IN ('已发布', '计划中')
        ORDER BY wm.area_id, wmr.meal_type
        """)
        
        try:
            all_result = db.session.execute(all_friday_sql, {
                'day_of_week': day_of_week
            })
            
            print("所有学校的周五菜单:")
            for row in all_result:
                school_name = row.school_name or f"学校ID:{row.area_id}"
                print(f"  {school_name}: {row.meal_type} - {row.recipe_name}")
                
        except Exception as e:
            print(f"查询所有学校数据失败: {str(e)}")

if __name__ == '__main__':
    debug_friday_menu()
