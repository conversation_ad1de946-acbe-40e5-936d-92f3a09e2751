#!/usr/bin/env python3
"""
调试脚本：检查今日菜单数据来源
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import MenuPlan, MenuRecipe, Recipe, WeeklyMenu, WeeklyMenuRecipe
from datetime import date, datetime
from sqlalchemy import text

def debug_menu_data():
    app = create_app()
    
    with app.app_context():
        today = date.today()
        print(f"=== 今日菜单数据调试 ({today}) ===\n")
        
        # 1. 检查日菜单计划 (MenuPlan)
        print("1. 检查日菜单计划 (menu_plans 表):")
        menu_plans = MenuPlan.query.filter_by(plan_date=today).all()
        print(f"   找到 {len(menu_plans)} 条今日菜单计划记录")
        
        for plan in menu_plans:
            print(f"   - ID: {plan.id}, 学校: {plan.area_id}, 餐次: {plan.meal_type}, 状态: {plan.status}")
            
            # 检查关联的菜谱
            menu_recipes = MenuRecipe.query.filter_by(menu_plan_id=plan.id).all()
            print(f"     关联菜谱数量: {len(menu_recipes)}")
            for mr in menu_recipes:
                recipe = Recipe.query.get(mr.recipe_id)
                recipe_name = recipe.name if recipe else "未知菜品"
                print(f"     - 菜谱: {recipe_name}, 数量: {mr.planned_quantity}")
        
        print()
        
        # 2. 检查周菜单 (WeeklyMenu)
        print("2. 检查周菜单 (weekly_menus 表):")
        
        # 计算今天是周几
        day_of_week = today.weekday() + 1  # 1=周一, 7=周日
        weekday_names = {1: '周一', 2: '周二', 3: '周三', 4: '周四', 5: '周五', 6: '周六', 7: '周日'}
        print(f"   今天是: {weekday_names[day_of_week]} (day_of_week={day_of_week})")
        
        # 查找包含今天的周菜单
        weekly_menus = WeeklyMenu.query.filter(
            WeeklyMenu.week_start <= today,
            WeeklyMenu.week_end >= today
        ).all()
        
        print(f"   找到 {len(weekly_menus)} 个包含今天的周菜单")
        
        for wm in weekly_menus:
            print(f"   - ID: {wm.id}, 学校: {wm.area_id}, 周期: {wm.week_start} ~ {wm.week_end}, 状态: {wm.status}")
            
            # 检查今天的菜谱
            weekly_recipes = WeeklyMenuRecipe.query.filter_by(
                weekly_menu_id=wm.id,
                day_of_week=day_of_week
            ).all()
            
            print(f"     今天的菜谱数量: {len(weekly_recipes)}")
            for wr in weekly_recipes:
                print(f"     - 餐次: {wr.meal_type}, 菜谱: {wr.recipe_name}")
        
        print()
        
        # 3. 模拟今日菜单API的查询逻辑
        print("3. 模拟今日菜单API查询逻辑:")
        
        # 假设用户学校ID为1（根据实际情况调整）
        test_area_ids = [1, 2, 3]  # 测试多个学校ID
        
        for area_id in test_area_ids:
            print(f"\n   测试学校ID: {area_id}")
            
            # 查询日菜单
            daily_plans = MenuPlan.query.filter_by(
                plan_date=today,
                area_id=area_id
            ).all()
            
            print(f"   日菜单计划: {len(daily_plans)} 条")
            
            if daily_plans:
                for plan in daily_plans:
                    recipes = []
                    for menu_recipe in plan.menu_recipes:
                        if menu_recipe.recipe:
                            recipes.append(menu_recipe.recipe.name)
                    print(f"     {plan.meal_type}: {', '.join(recipes) if recipes else '无菜谱'}")
            else:
                print("     无日菜单，查询周菜单...")
                
                # 查询周菜单
                menu_sql = text("""
                SELECT wmr.recipe_name, wmr.meal_type
                FROM weekly_menu_recipes wmr
                INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
                WHERE wmr.day_of_week = :day_of_week
                AND wm.area_id = :area_id
                AND wm.status IN ('已发布', '计划中')
                ORDER BY wmr.meal_type, wmr.id
                """)
                
                result = db.session.execute(menu_sql, {
                    'day_of_week': day_of_week,
                    'area_id': area_id
                })
                
                weekly_recipes = {}
                for row in result:
                    meal_type = row.meal_type
                    if meal_type not in weekly_recipes:
                        weekly_recipes[meal_type] = []
                    weekly_recipes[meal_type].append(row.recipe_name)
                
                if weekly_recipes:
                    print("     周菜单数据:")
                    for meal_type, recipes in weekly_recipes.items():
                        print(f"       {meal_type}: {', '.join(recipes)}")
                else:
                    print("     周菜单也无数据")
        
        print()
        
        # 4. 检查所有表的数据总量
        print("4. 数据库表数据总量:")
        print(f"   menu_plans: {MenuPlan.query.count()} 条")
        print(f"   menu_recipes: {MenuRecipe.query.count()} 条")
        print(f"   weekly_menus: {WeeklyMenu.query.count()} 条")
        print(f"   weekly_menu_recipes: {WeeklyMenuRecipe.query.count()} 条")
        print(f"   recipes: {Recipe.query.count()} 条")

if __name__ == '__main__':
    debug_menu_data()
