"""
仪表盘API路由

提供仪表盘API接口，用于前端调用。
"""

from flask import Blueprint, jsonify, request
from flask_login import login_required, current_user
from app.services.dashboard_service import DashboardService
from app.models_daily_management import DiningCompanion, DailyLog
from app.models import MenuPlan
from app import db
from sqlalchemy import desc, text
from datetime import date, datetime
from flask import current_app

# 创建蓝图
dashboard_api_bp = Blueprint('dashboard_api', __name__)

@dashboard_api_bp.route('/api/v2/dashboard/summary', methods=['GET'])
@login_required
def api_v2_dashboard_summary():
    """获取仪表盘摘要"""
    date_str = request.args.get('date')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_dashboard_summary(date_str, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/weekly', methods=['GET'])
@login_required
def api_v2_weekly_summary():
    """获取周摘要"""
    week_start = request.args.get('week_start')
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_weekly_summary(week_start, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dashboard/monthly', methods=['GET'])
@login_required
def api_v2_monthly_summary():
    """获取月摘要"""
    year = request.args.get('year', type=int)
    month = request.args.get('month', type=int)
    area_id = request.args.get('area_id', type=int)

    summary = DashboardService.get_monthly_summary(year, month, area_id)
    return jsonify(summary)

@dashboard_api_bp.route('/api/v2/dining-companions/recent', methods=['GET'])
@login_required
def api_v2_recent_dining_companions():
    """获取最近的陪餐记录"""
    try:
        limit = request.args.get('limit', 5, type=int)
        current_app.logger.info(f"开始查询最近 {limit} 条陪餐记录")

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            return jsonify([])

        # 使用 JOIN 查询优化性能，添加区域过滤
        companions = db.session.query(DiningCompanion, DailyLog)\
            .join(DailyLog, DiningCompanion.daily_log_id == DailyLog.id)\
            .filter(DailyLog.area_id == user_area.id)\
            .order_by(desc(DiningCompanion.dining_time))\
            .limit(limit)\
            .all()
        
        result = []
        for companion, log in companions:
            try:
                record = {
                    'id': companion.id,
                    'name': companion.companion_name or '匿名',
                    'role': companion.companion_role or '未知',
                    'time': companion.dining_time.strftime('%H:%M') if companion.dining_time else '',
                    'date': log.log_date.strftime('%Y-%m-%d') if log and log.log_date else '',
                    'meal_type': companion.meal_type or '',
                    'taste_rating': companion.taste_rating or 0,
                    'hygiene_rating': companion.hygiene_rating or 0,
                    'service_rating': companion.service_rating or 0
                }
                result.append(record)
            except Exception as e:
                current_app.logger.error(f"处理陪餐记录 {companion.id} 时出错: {str(e)}")
                continue
        
        current_app.logger.info(f"成功获取 {len(result)} 条陪餐记录")
        return jsonify(result)
        
    except Exception as e:
        current_app.logger.error(f"获取最近陪餐记录失败: {str(e)}")
        return jsonify({
            'error': '获取陪餐记录失败',
            'message': str(e)
        }), 500

@dashboard_api_bp.route('/api/v2/dashboard/today-menu', methods=['GET'])
@login_required
def api_v2_today_menu():
    """获取今日菜单"""
    try:
        today_date = date.today()

        menu_data = {
            '早餐': {'recipes': [], 'status': '暂无菜单'},
            '午餐': {'recipes': [], 'status': '暂无菜单'},
            '晚餐': {'recipes': [], 'status': '暂无菜单'}
        }

        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            return jsonify({'success': True, 'data': menu_data})

        # 查询今日菜单计划，添加区域过滤
        plans = MenuPlan.query.filter_by(
            plan_date=today_date,
            area_id=user_area.id
        ).all()

        for plan in plans:
            if plan.meal_type in menu_data:
                menu_data[plan.meal_type]['status'] = plan.status or '计划中'

                # 获取菜谱
                recipes = []
                for menu_recipe in plan.menu_recipes:
                    if menu_recipe.recipe:
                        recipes.append({
                            'name': menu_recipe.recipe.name,
                            'quantity': menu_recipe.planned_quantity
                        })

                menu_data[plan.meal_type]['recipes'] = recipes

        # 如果某个餐次没有日菜单，尝试从周菜单获取
        for meal_type in menu_data:
            if not menu_data[meal_type]['recipes']:  # 如果没有菜谱数据
                # 获取当天是周几（1-7，1表示周一）
                day_of_week = today_date.weekday() + 1

                # 查询当天的菜谱信息（按日期范围筛选）
                menu_sql = text("""
                SELECT wmr.recipe_name
                FROM weekly_menu_recipes wmr
                INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
                WHERE wmr.day_of_week = :day_of_week
                AND wmr.meal_type = :meal_type
                AND wm.area_id = :area_id
                AND wm.status IN ('已发布', '计划中')
                AND wm.week_start <= :today
                AND wm.week_end >= :today
                ORDER BY wm.created_at DESC, wmr.id
                """)

                menu_result = db.session.execute(menu_sql, {
                    'day_of_week': day_of_week,
                    'meal_type': meal_type,
                    'area_id': user_area.id,
                    'today': today_date
                })

                recipes = []
                for row in menu_result:
                    recipes.append({
                        'name': row.recipe_name,
                        'quantity': None
                    })

                if recipes:
                    menu_data[meal_type]['recipes'] = recipes
                    menu_data[meal_type]['status'] = '周菜单'

        return jsonify({
            'success': True,
            'data': menu_data,
            'date': today_date.strftime('%Y-%m-%d')
        })

    except Exception as e:
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@dashboard_api_bp.route('/api/v2/dashboard/supply-chain-status', methods=['GET'])
@login_required
def api_v2_supply_chain_status():
    """获取供应链状态数据"""
    try:
        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            return jsonify({
                'success': False,
                'message': '用户未关联学校'
            })

        today = date.today()

        # 采购订单统计
        purchase_orders_sql = text("""
            SELECT
                COUNT(CASE WHEN status = '待确认' THEN 1 END) as pending_count,
                COUNT(CASE WHEN status = '已完成' THEN 1 END) as completed_count,
                COALESCE(SUM(CASE WHEN status IN ('待确认', '已完成') THEN total_amount END), 0) as total_amount
            FROM purchase_orders
            WHERE area_id = :area_id
            AND created_at >= DATEADD(day, -30, GETDATE())
        """)

        purchase_result = db.session.execute(purchase_orders_sql, {'area_id': user_area.id}).fetchone()

        # 入库统计
        stock_in_sql = text("""
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = :today THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = :area_id
            AND created_at >= DATEADD(day, -7, GETDATE())
        """)

        stock_in_result = db.session.execute(stock_in_sql, {
            'area_id': user_area.id,
            'today': today
        }).fetchone()

        # 消耗计划统计
        consumption_plan_sql = text("""
            SELECT
                COUNT(CASE WHEN plan_date = :today THEN 1 END) as today_plans,
                CASE
                    WHEN COUNT(CASE WHEN plan_date = :today THEN 1 END) > 0 THEN
                        CAST(AVG(CASE WHEN plan_date = :today AND status = '已执行' THEN 100.0 ELSE 0 END) AS DECIMAL(5,1))
                    ELSE 0
                END as execution_rate,
                COALESCE(SUM(CASE WHEN plan_date = :today THEN estimated_cost END), 0) as estimated_cost
            FROM consumption_plans
            WHERE area_id = :area_id
            AND plan_date >= DATEADD(day, -7, GETDATE())
        """)

        consumption_result = db.session.execute(consumption_plan_sql, {
            'area_id': user_area.id,
            'today': today
        }).fetchone()

        # 出库统计
        stock_out_sql = text("""
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = :today THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待出库' THEN 1 END) as pending_batches
            FROM stock_out_records
            WHERE area_id = :area_id
            AND created_at >= DATEADD(day, -7, GETDATE())
        """)

        stock_out_result = db.session.execute(stock_out_sql, {
            'area_id': user_area.id,
            'today': today
        }).fetchone()

        # 构建返回数据
        supply_chain_data = {
            'purchase_orders': {
                'pending': purchase_result.pending_count if purchase_result else 0,
                'completed': purchase_result.completed_count if purchase_result else 0,
                'total_amount': float(purchase_result.total_amount) if purchase_result and purchase_result.total_amount else 0
            },
            'stock_in': {
                'today_batches': stock_in_result.today_batches if stock_in_result else 0,
                'pending_inspection': stock_in_result.pending_inspection if stock_in_result else 0,
                'quality_rate': float(stock_in_result.quality_rate) if stock_in_result and stock_in_result.quality_rate else 0
            },
            'consumption_plan': {
                'today_plans': consumption_result.today_plans if consumption_result else 0,
                'execution_rate': float(consumption_result.execution_rate) if consumption_result and consumption_result.execution_rate else 0,
                'estimated_cost': float(consumption_result.estimated_cost) if consumption_result and consumption_result.estimated_cost else 0
            },
            'stock_out': {
                'today_batches': stock_out_result.today_batches if stock_out_result else 0,
                'pending_batches': stock_out_result.pending_batches if stock_out_result else 0,
                'turnover_status': '正常'  # 可以根据实际业务逻辑计算
            }
        }

        return jsonify({
            'success': True,
            'data': supply_chain_data
        })

    except Exception as e:
        current_app.logger.error(f"获取供应链状态失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取供应链状态失败',
            'error': str(e)
        }), 500

@dashboard_api_bp.route('/api/v2/dashboard/notifications', methods=['GET'])
@login_required
def api_v2_dashboard_notifications():
    """获取仪表盘通知消息"""
    try:
        # 获取用户所属学校
        user_area = current_user.get_current_area()
        if not user_area:
            return jsonify({
                'success': False,
                'message': '用户未关联学校'
            })

        notifications = []

        # 检查库存预警
        inventory_sql = text("""
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = :area_id
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        """)

        inventory_result = db.session.execute(inventory_sql, {'area_id': user_area.id}).fetchall()

        if inventory_result:
            for item in inventory_result:
                notifications.append({
                    'icon': 'fas fa-exclamation-triangle',
                    'color': 'warning',
                    'type': '库存预警：',
                    'message': f'{item.ingredient_name}库存不足（剩余{item.quantity}{item.unit}）'
                })

        # 检查采购订单状态
        purchase_sql = text("""
            SELECT COUNT(*) as pending_count
            FROM purchase_orders
            WHERE area_id = :area_id
            AND status = '待确认'
            AND created_at >= DATEADD(day, -7, GETDATE())
        """)

        purchase_result = db.session.execute(purchase_sql, {'area_id': user_area.id}).fetchone()

        if purchase_result and purchase_result.pending_count > 0:
            notifications.append({
                'icon': 'fas fa-truck',
                'color': 'info',
                'type': '采购订单：',
                'message': f'今日有{purchase_result.pending_count}个订单待确认收货'
            })

        # 检查今日检查状态
        today = date.today()
        inspection_sql = text("""
            SELECT
                COUNT(CASE WHEN meal_type = '早餐' AND status = '已完成' THEN 1 END) as morning_done,
                COUNT(CASE WHEN meal_type = '午餐' AND status = '已完成' THEN 1 END) as lunch_done,
                COUNT(CASE WHEN meal_type = '晚餐' AND status = '已完成' THEN 1 END) as dinner_done,
                COUNT(CASE WHEN status = '待整改' THEN 1 END) as issues_count
            FROM daily_inspections
            WHERE area_id = :area_id
            AND inspection_date = :today
        """)

        inspection_result = db.session.execute(inspection_sql, {
            'area_id': user_area.id,
            'today': today
        }).fetchone()

        if inspection_result:
            if inspection_result.issues_count > 0:
                notifications.append({
                    'icon': 'fas fa-exclamation-triangle',
                    'color': 'danger',
                    'type': '检查提醒：',
                    'message': f'发现{inspection_result.issues_count}项待整改问题'
                })
            elif inspection_result.morning_done > 0 and inspection_result.lunch_done == 0:
                notifications.append({
                    'icon': 'fas fa-clipboard-check',
                    'color': 'success',
                    'type': '检查提醒：',
                    'message': '晨检记录已完成，午检待进行'
                })
            elif inspection_result.morning_done > 0 and inspection_result.lunch_done > 0:
                notifications.append({
                    'icon': 'fas fa-clipboard-check',
                    'color': 'success',
                    'type': '检查提醒：',
                    'message': '所有检查项目已完成'
                })

        # 如果没有通知，添加默认通知
        if not notifications:
            notifications.append({
                'icon': 'fas fa-check-circle',
                'color': 'success',
                'type': '系统状态：',
                'message': '一切正常，无需特别关注的事项'
            })

        return jsonify({
            'success': True,
            'data': notifications
        })

    except Exception as e:
        current_app.logger.error(f"获取通知消息失败: {str(e)}")
        return jsonify({
            'success': False,
            'message': '获取通知消息失败',
            'error': str(e)
        }), 500


