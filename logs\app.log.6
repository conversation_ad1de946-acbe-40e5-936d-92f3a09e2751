2025-06-04 18:38:11,676 WARNING: Suspicious path blocked: /admin/videos/upload from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:38:11,676 WARNING: Suspicious path blocked: /admin/videos/upload from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:38:11,691 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:38:11,691 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:57:44,089 WARNING: Suspicious path blocked: /admin/guide/videos from 14.103.175.118 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:57:44,104 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:57:57,555 WARNING: Suspicious path blocked: /admin/guide/videos from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:57:57,556 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:59:02,880 WARNING: Suspicious path blocked: /admin/guide/videos/upload from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:59:02,880 WARNING: Suspicious path blocked: /admin/guide/videos/upload from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:59:02,880 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:59:02,881 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 19:03:26,629 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 19:05:28,000 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 19:10:35,101 INFO: 应用启动 [in .\app\__init__.py:789]
2025-06-04 19:12:34,091 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 19:24:08,687 INFO: 视频上传成功: 步骤=weekly_menu, 文件=20250604192408_c33d6ff1_WEEEK.mp4, 大小=9786990字节 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:216]
2025-06-04 19:24:11,118 INFO: 视频上传成功: 步骤=weekly_menu, 文件=20250604192411_1d64c364_WEEEK.mp4, 大小=9786990字节 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:216]
2025-06-04 19:25:01,495 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 19:25:20,833 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 19:30:51,958 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 19:39:08,609 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 19:49:23,552 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 19:54:44,951 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 19:55:59,691 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 19:56:20,944 INFO: 视频上传成功: 步骤=weekly_menu, 文件=20250604195620_87170a2c_WEEEK.mp4, 大小=9786990字节 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:216]
2025-06-04 19:56:23,943 INFO: 视频上传成功: 步骤=weekly_menu, 文件=20250604195623_1c226b2e_WEEEK.mp4, 大小=9786990字节 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:216]
2025-06-04 19:58:14,047 INFO: 更新视频请求参数: step_name=, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 19:58:14,048 ERROR: 缺少必要参数: step_name=, new_video_name=菜单计划创建, original_video_name=菜单计划创建 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:321]
2025-06-04 19:58:18,795 INFO: 更新视频请求参数: step_name=, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 19:58:18,795 ERROR: 缺少必要参数: step_name=, new_video_name=菜单计划创建, original_video_name=菜单计划创建 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:321]
2025-06-04 20:04:26,157 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:04:58,108 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 20:11:20,081 INFO: 更新视频请求参数: step_name=, new_video_name=新建周菜单, original_video_name=新建周菜单, description=新建周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:11:20,081 ERROR: 缺少必要参数: step_name=, new_video_name=新建周菜单, original_video_name=新建周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:321]
2025-06-04 20:11:25,893 INFO: 更新视频请求参数: step_name=, new_video_name=新建周菜单, original_video_name=新建周菜单, description=新建周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:11:25,894 ERROR: 缺少必要参数: step_name=, new_video_name=新建周菜单, original_video_name=新建周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:321]
2025-06-04 20:32:59,076 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:33:20,066 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 20:33:57,897 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:34:00,779 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:37:09,438 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:37:12,154 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:38:30,733 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:38:57,107 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:42:14,446 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:42:17,006 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
