2025-06-04 18:27:17,185 ERROR: 周菜单操作异常: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py:115]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py", line 104, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 122, in plan
    area = AdministrativeArea.query.get_or_404(area_id)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask_sqlalchemy\query.py", line 33, in get_or_404
    abort(404, description=description)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\flask\helpers.py", line 272, in abort
    current_app.aborter(code, *args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\werkzeug\exceptions.py", line 863, in __call__
    raise self.mapping[code](*args, **kwargs)
werkzeug.exceptions.NotFound: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again.
2025-06-04 18:27:23,826 ERROR: 周菜单操作异常: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]在应使用条件的上下文(在 'OR' 附近)中指定了非布尔类型的表达式。 (4145) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: SELECT TOP 500 recipes.id AS recipes_id, recipes.name AS recipes_name, recipes.category AS recipes_category, recipes.category_id AS recipes_category_id, recipes.meal_type AS recipes_meal_type, recipes.main_image AS recipes_main_image, recipes.description AS recipes_description, recipes.calories AS recipes_calories, recipes.nutrition_info AS recipes_nutrition_info, recipes.cooking_method AS recipes_cooking_method, recipes.cooking_steps AS recipes_cooking_steps, recipes.cooking_time AS recipes_cooking_time, recipes.serving_size AS recipes_serving_size, recipes.status AS recipes_status, recipes.created_by AS recipes_created_by, recipes.created_at AS recipes_created_at, recipes.updated_at AS recipes_updated_at, recipes.parent_id AS recipes_parent_id, recipes.is_template AS recipes_is_template, recipes.template_type AS recipes_template_type, recipes.variation_reason AS recipes_variation_reason, recipes.version AS recipes_version, recipes.is_user_defined AS recipes_is_user_defined, recipes.priority AS recipes_priority, recipes.area_id AS recipes_area_id, recipes.is_global AS recipes_is_global, recipes.is_deleted AS recipes_is_deleted, recipes.deleted_at AS recipes_deleted_at, recipes.deleted_by AS recipes_deleted_by 
FROM recipes 
WHERE recipes.status = ? AND (NULL OR recipes.is_global = 1 OR recipes.area_id IS NULL) ORDER BY CASE WHEN (recipes.area_id IS NULL) THEN ? WHEN (recipes.is_global = 1) THEN ? ELSE ? END, recipes.priority DESC, recipes.id DESC]
[parameters: (1, 1, 2, 3)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py:115]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
pyodbc.ProgrammingError: ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]在应使用条件的上下文(在 'OR' 附近)中指定了非布尔类型的表达式。 (4145) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py", line 104, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 274, in plan
    recipes = recipes_query.all()
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2673, in all
    return self._iter().all()  # type: ignore
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\query.py", line 2827, in _iter
    result: Union[ScalarResult[_T], Result[_T]] = self.session.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2351, in execute
    return self._execute_internal(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\session.py", line 2236, in _execute_internal
    result: Result[Any] = compile_state_cls.orm_execute_statement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\orm\context.py", line 293, in orm_execute_statement
    result = conn.execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1418, in execute
    return meth(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\sql\elements.py", line 515, in _execute_on_connection
    return connection._execute_clauseelement(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1640, in _execute_clauseelement
    ret = self._execute_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1846, in _execute_context
    return self._exec_single_context(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1986, in _exec_single_context
    self._handle_dbapi_exception(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 2353, in _handle_dbapi_exception
    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\base.py", line 1967, in _exec_single_context
    self.dialect.do_execute(
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\venv\lib\site-packages\sqlalchemy\engine\default.py", line 924, in do_execute
    cursor.execute(statement, parameters)
sqlalchemy.exc.ProgrammingError: (pyodbc.ProgrammingError) ('42000', "[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]在应使用条件的上下文(在 'OR' 附近)中指定了非布尔类型的表达式。 (4145) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: SELECT TOP 500 recipes.id AS recipes_id, recipes.name AS recipes_name, recipes.category AS recipes_category, recipes.category_id AS recipes_category_id, recipes.meal_type AS recipes_meal_type, recipes.main_image AS recipes_main_image, recipes.description AS recipes_description, recipes.calories AS recipes_calories, recipes.nutrition_info AS recipes_nutrition_info, recipes.cooking_method AS recipes_cooking_method, recipes.cooking_steps AS recipes_cooking_steps, recipes.cooking_time AS recipes_cooking_time, recipes.serving_size AS recipes_serving_size, recipes.status AS recipes_status, recipes.created_by AS recipes_created_by, recipes.created_at AS recipes_created_at, recipes.updated_at AS recipes_updated_at, recipes.parent_id AS recipes_parent_id, recipes.is_template AS recipes_is_template, recipes.template_type AS recipes_template_type, recipes.variation_reason AS recipes_variation_reason, recipes.version AS recipes_version, recipes.is_user_defined AS recipes_is_user_defined, recipes.priority AS recipes_priority, recipes.area_id AS recipes_area_id, recipes.is_global AS recipes_is_global, recipes.is_deleted AS recipes_is_deleted, recipes.deleted_at AS recipes_deleted_at, recipes.deleted_by AS recipes_deleted_by 
FROM recipes 
WHERE recipes.status = ? AND (NULL OR recipes.is_global = 1 OR recipes.area_id IS NULL) ORDER BY CASE WHEN (recipes.area_id IS NULL) THEN ? WHEN (recipes.is_global = 1) THEN ? ELSE ? END, recipes.priority DESC, recipes.id DESC]
[parameters: (1, 1, 2, 3)]
(Background on this error at: https://sqlalche.me/e/20/f405)
