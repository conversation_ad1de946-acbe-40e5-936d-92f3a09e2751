#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频引导服务
为用户引导系统提供视频演示功能 - 完全基于数据库驱动
"""

class VideoGuideService:
    """视频引导服务类 - 完全基于数据库驱动，自动发现所有模块"""

    @staticmethod
    def get_all_step_names():
        """从数据库动态获取所有步骤名称"""
        try:
            from app.models import VideoGuide
            from sqlalchemy import distinct

            # 从数据库获取所有不同的step_name
            db_steps = VideoGuide.query.with_entities(distinct(VideoGuide.step_name)).all()
            return [step[0] for step in db_steps if step[0]]
        except Exception:
            return []

    @staticmethod
    def get_step_display_name(step_name):
        """生成步骤的友好显示名称"""
        if not step_name:
            return '未知模块'

        # 简单的名称转换规则
        name_mappings = {
            'daily_management': '日常管理模块',
            'suppliers': '供应商管理',
            'ingredients_recipes': '食材食谱管理',
            'weekly_menu': '周菜单制定',
            'purchase_order': '采购订单管理',
            'stock_in': '食材入库管理',
            'consumption_plan': '消耗量计划',
            'stock_out': '食材出库管理',
            'traceability': '食材溯源管理',
            'food_samples': '留样记录管理'
        }

        return name_mappings.get(step_name, step_name.replace('_', ' ').title())

    @staticmethod
    def get_all_step_names():
        """动态获取所有步骤名称 - 从数据库自动发现"""
        try:
            from app.models import VideoGuide
            from sqlalchemy import distinct

            # 从数据库获取所有不同的step_name
            db_steps = VideoGuide.query.with_entities(distinct(VideoGuide.step_name)).all()
            db_step_names = [step[0] for step in db_steps if step[0]]

            # 合并默认步骤和数据库中的步骤
            all_steps = set(VideoGuideService.DEFAULT_STEP_NAMES.keys())
            all_steps.update(db_step_names)

            return list(all_steps)
        except Exception:
            # 如果出错，返回默认步骤
            return list(VideoGuideService.DEFAULT_STEP_NAMES.keys())

    @staticmethod
    def get_step_display_name(step_name):
        """获取步骤的显示名称"""
        # 首先检查默认映射
        if step_name in VideoGuideService.DEFAULT_STEP_NAMES:
            return VideoGuideService.DEFAULT_STEP_NAMES[step_name]

        # 如果没有默认映射，生成友好的显示名称
        # 将下划线替换为空格，首字母大写
        display_name = step_name.replace('_', ' ').title()

        # 一些常见的中文映射
        chinese_mappings = {
            'management': '管理',
            'system': '系统',
            'record': '记录',
            'plan': '计划',
            'order': '订单',
            'menu': '菜单',
            'stock': '库存',
            'food': '食品',
            'daily': '日常',
            'weekly': '周',
            'monthly': '月',
            'report': '报告',
            'analysis': '分析'
        }

        # 尝试转换为中文
        for en, zh in chinese_mappings.items():
            if en in step_name.lower():
                display_name = display_name.replace(en.title(), zh)

        return display_name

    @staticmethod
    def get_video_resources(step_name):
        """获取指定步骤的视频资源 - 完全基于数据库"""
        try:
            from app.models import VideoGuide

            # 从数据库获取视频
            db_videos = VideoGuide.query.filter_by(step_name=step_name).order_by(VideoGuide.created_at.desc()).all()

            videos_list = []
            for video in db_videos:
                # 安全处理created_at字段
                created_at_str = ''
                if video.created_at:
                    try:
                        if hasattr(video.created_at, 'strftime'):
                            created_at_str = video.created_at.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            created_at_str = str(video.created_at)
                    except Exception:
                        created_at_str = str(video.created_at)

                video_dict = {
                    'id': video.id,
                    'name': video.name,
                    'url': video.file_path,
                    'thumbnail': video.thumbnail or '/static/images/video_thumbnails/default.jpg',
                    'duration': video.duration or '未知',
                    'description': video.description or '',
                    'created_at': created_at_str
                }
                videos_list.append(video_dict)

            return {
                'title': VideoGuideService.STEP_NAMES.get(step_name, '系统引导'),
                'duration': f"{len(videos_list)}个视频" if videos_list else "暂无视频",
                'videos': videos_list,
                'source': 'database'
            }

        except Exception as e:
            # 出错时返回空结果
            return {
                'title': VideoGuideService.STEP_NAMES.get(step_name, '系统引导'),
                'duration': "暂无视频",
                'videos': [],
                'source': 'database',
                'error': str(e)
            }

    @staticmethod
    def get_all_videos():
        """获取所有视频资源 - 完全基于数据库"""
        try:
            from app.models import VideoGuide

            # 获取所有数据库中的视频
            all_db_videos = VideoGuide.query.order_by(VideoGuide.step_name, VideoGuide.created_at.desc()).all()

            # 动态获取所有步骤名称
            all_step_names = VideoGuideService.get_all_step_names()

            # 按步骤分组数据库视频
            resources = {}
            for step_name in all_step_names:
                step_videos = [v for v in all_db_videos if v.step_name == step_name]

                videos_list = []
                for video in step_videos:
                    # 安全处理created_at字段
                    created_at_str = ''
                    if video.created_at:
                        try:
                            if hasattr(video.created_at, 'strftime'):
                                created_at_str = video.created_at.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                created_at_str = str(video.created_at)
                        except Exception:
                            created_at_str = str(video.created_at)

                    video_dict = {
                        'id': video.id,
                        'name': video.name,
                        'url': video.file_path,
                        'thumbnail': video.thumbnail or '/static/images/video_thumbnails/default.jpg',
                        'duration': video.duration or '未知',
                        'description': video.description or '',
                        'created_at': created_at_str
                    }
                    videos_list.append(video_dict)

                resources[step_name] = {
                    'title': VideoGuideService.get_step_display_name(step_name),
                    'duration': f"{len(videos_list)}个视频" if videos_list else "暂无视频",
                    'videos': videos_list,
                    'source': 'database'
                }

            return resources

        except Exception as e:
            # 出错时返回空结构
            return {
                'error': str(e),
                'message': '获取视频资源失败'
            }

    @staticmethod
    def get_video_playlist(step_names):
        """获取指定步骤的视频播放列表 - 基于数据库"""
        playlist = []
        for step_name in step_names:
            step_videos = VideoGuideService.get_video_resources(step_name)
            if step_videos and 'videos' in step_videos:
                for video in step_videos['videos']:
                    playlist.append({
                        'step': step_name,
                        'step_title': step_videos['title'],
                        **video
                    })
        return playlist

    @staticmethod
    def get_video_analytics():
        """获取视频观看统计 - 基于数据库"""
        try:
            from app.models import VideoGuide
            from sqlalchemy import func

            # 统计数据库中的视频
            total_videos = VideoGuide.query.count()

            # 按步骤统计 - 使用SQL聚合查询
            step_counts_query = VideoGuide.query.with_entities(
                VideoGuide.step_name,
                func.count(VideoGuide.id).label('count')
            ).group_by(VideoGuide.step_name).all()

            # 转换为字典，包含所有步骤
            step_counts = {}
            for step_name in VideoGuideService.STEP_NAMES.keys():
                step_counts[step_name] = 0

            # 填入实际统计数据
            for step_name, count in step_counts_query:
                if step_name in step_counts:
                    step_counts[step_name] = count

            # 找出视频最多的步骤
            max_count = max(step_counts.values()) if step_counts else 0
            most_watched = '暂无'
            if max_count > 0:
                for step_name, count in step_counts.items():
                    if count == max_count:
                        most_watched = step_name
                        break

            return {
                'total_videos': total_videos,
                'total_duration': f"{total_videos}个视频",
                'most_watched': VideoGuideService.STEP_NAMES.get(most_watched, '暂无'),
                'completion_rate': 1.0 if total_videos > 0 else 0.0,
                'step_counts': step_counts
            }
        except Exception as e:
            return {
                'total_videos': 0,
                'total_duration': '0个视频',
                'most_watched': '暂无',
                'completion_rate': 0.0,
                'step_counts': {},
                'error': str(e)
            }

    @staticmethod
    def get_step_video_count(step_name):
        """获取指定步骤的视频数量"""
        try:
            from app.models import VideoGuide
            return VideoGuide.query.filter_by(step_name=step_name).count()
        except Exception:
            return 0

    @staticmethod
    def has_videos(step_name):
        """检查指定步骤是否有视频"""
        return VideoGuideService.get_step_video_count(step_name) > 0
