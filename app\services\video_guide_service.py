#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
视频引导服务
为用户引导系统提供视频演示功能 - 完全基于数据库驱动
"""

# 不再需要静态导入，所有功能都基于数据库

class VideoGuideService:
    """视频引导服务类 - 完全基于数据库驱动"""

    # 步骤名称映射
    STEP_NAMES = {
        'daily_management': '日常管理模块',
        'suppliers': '供应商管理',
        'ingredients_recipes': '食材食谱管理',
        'weekly_menu': '周菜单制定',
        'purchase_order': '采购订单管理',
        'stock_in': '食材入库管理',
        'consumption_plan': '消耗量计划',
        'stock_out': '食材出库管理',
        'traceability': '食材溯源管理',
        'food_samples': '留样记录管理'
    }

    @staticmethod
    def get_video_resources(step_name):
        """获取指定步骤的视频资源 - 完全基于数据库"""
        try:
            from app.models import VideoGuide

            # 从数据库获取视频
            db_videos = VideoGuide.query.filter_by(step_name=step_name).order_by(VideoGuide.created_at.desc()).all()

            videos_list = []
            for video in db_videos:
                # 安全处理created_at字段
                created_at_str = ''
                if video.created_at:
                    try:
                        if hasattr(video.created_at, 'strftime'):
                            created_at_str = video.created_at.strftime('%Y-%m-%d %H:%M:%S')
                        else:
                            created_at_str = str(video.created_at)
                    except Exception:
                        created_at_str = str(video.created_at)

                video_dict = {
                    'id': video.id,
                    'name': video.name,
                    'url': video.file_path,
                    'thumbnail': video.thumbnail or '/static/images/video_thumbnails/default.jpg',
                    'duration': video.duration or '未知',
                    'description': video.description or '',
                    'created_at': created_at_str
                }
                videos_list.append(video_dict)

            return {
                'title': VideoGuideService.STEP_NAMES.get(step_name, '系统引导'),
                'duration': f"{len(videos_list)}个视频" if videos_list else "暂无视频",
                'videos': videos_list,
                'source': 'database'
            }

        except Exception as e:
            # 出错时返回空结果
            return {
                'title': VideoGuideService.STEP_NAMES.get(step_name, '系统引导'),
                'duration': "暂无视频",
                'videos': [],
                'source': 'database',
                'error': str(e)
            }

    @staticmethod
    def get_all_videos():
        """获取所有视频资源 - 完全基于数据库"""
        try:
            from app.models import VideoGuide

            # 获取所有数据库中的视频
            all_db_videos = VideoGuide.query.order_by(VideoGuide.step_name, VideoGuide.created_at.desc()).all()

            # 按步骤分组数据库视频
            resources = {}
            for step_name in VideoGuideService.STEP_NAMES.keys():
                step_videos = [v for v in all_db_videos if v.step_name == step_name]

                videos_list = []
                for video in step_videos:
                    # 安全处理created_at字段
                    created_at_str = ''
                    if video.created_at:
                        try:
                            if hasattr(video.created_at, 'strftime'):
                                created_at_str = video.created_at.strftime('%Y-%m-%d %H:%M:%S')
                            else:
                                created_at_str = str(video.created_at)
                        except Exception:
                            created_at_str = str(video.created_at)

                    video_dict = {
                        'id': video.id,
                        'name': video.name,
                        'url': video.file_path,
                        'thumbnail': video.thumbnail or '/static/images/video_thumbnails/default.jpg',
                        'duration': video.duration or '未知',
                        'description': video.description or '',
                        'created_at': created_at_str
                    }
                    videos_list.append(video_dict)

                resources[step_name] = {
                    'title': VideoGuideService.STEP_NAMES[step_name],
                    'duration': f"{len(videos_list)}个视频" if videos_list else "暂无视频",
                    'videos': videos_list,
                    'source': 'database'
                }

            return resources

        except Exception as e:
            # 出错时返回空结构
            resources = {}
            for step_name, step_title in VideoGuideService.STEP_NAMES.items():
                resources[step_name] = {
                    'title': step_title,
                    'duration': "暂无视频",
                    'videos': [],
                    'source': 'database',
                    'error': str(e)
                }
            return resources

    @staticmethod
    def get_video_playlist(step_names):
        """获取指定步骤的视频播放列表 - 基于数据库"""
        playlist = []
        for step_name in step_names:
            step_videos = VideoGuideService.get_video_resources(step_name)
            if step_videos and 'videos' in step_videos:
                for video in step_videos['videos']:
                    playlist.append({
                        'step': step_name,
                        'step_title': step_videos['title'],
                        **video
                    })
        return playlist

    @staticmethod
    def get_video_analytics():
        """获取视频观看统计 - 基于数据库"""
        try:
            from app.models import VideoGuide
            from sqlalchemy import func

            # 统计数据库中的视频
            total_videos = VideoGuide.query.count()

            # 按步骤统计 - 使用SQL聚合查询
            step_counts_query = VideoGuide.query.with_entities(
                VideoGuide.step_name,
                func.count(VideoGuide.id).label('count')
            ).group_by(VideoGuide.step_name).all()

            # 转换为字典，包含所有步骤
            step_counts = {}
            for step_name in VideoGuideService.STEP_NAMES.keys():
                step_counts[step_name] = 0

            # 填入实际统计数据
            for step_name, count in step_counts_query:
                if step_name in step_counts:
                    step_counts[step_name] = count

            # 找出视频最多的步骤
            max_count = max(step_counts.values()) if step_counts else 0
            most_watched = '暂无'
            if max_count > 0:
                for step_name, count in step_counts.items():
                    if count == max_count:
                        most_watched = step_name
                        break

            return {
                'total_videos': total_videos,
                'total_duration': f"{total_videos}个视频",
                'most_watched': VideoGuideService.STEP_NAMES.get(most_watched, '暂无'),
                'completion_rate': 1.0 if total_videos > 0 else 0.0,
                'step_counts': step_counts
            }
        except Exception as e:
            return {
                'total_videos': 0,
                'total_duration': '0个视频',
                'most_watched': '暂无',
                'completion_rate': 0.0,
                'step_counts': {},
                'error': str(e)
            }

    @staticmethod
    def get_step_video_count(step_name):
        """获取指定步骤的视频数量"""
        try:
            from app.models import VideoGuide
            return VideoGuide.query.filter_by(step_name=step_name).count()
        except Exception:
            return 0

    @staticmethod
    def has_videos(step_name):
        """检查指定步骤是否有视频"""
        return VideoGuideService.get_step_video_count(step_name) > 0
