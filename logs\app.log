2025-06-06 20:43:03,278 INFO: 更新了日期 2025-06-01 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:43:07,086 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:43:07,100 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:43:41,503 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:43:41,522 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:43:48,352 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:43:48,369 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:46:39,426 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:46:39,443 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:47:50,698 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:47:50,709 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:48:43,129 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:48:43,137 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:49:38,806 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:49:38,821 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:49:56,543 ERROR: 周菜单操作异常: time data 'undefined' does not match format '%Y-%m-%d' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py:115]
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\decorators.py", line 104, in wrapper
    return func(*args, **kwargs)
  File "C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\weekly_menu_v2.py", line 130, in plan
    week_start = datetime.strptime(week_start_str, '%Y-%m-%d')
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_strptime.py", line 568, in _strptime_datetime
    tt, fraction, gmtoff_fraction = _strptime(data_string, format)
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python38\lib\_strptime.py", line 349, in _strptime
    raise ValueError("time data %r does not match format %r" %
ValueError: time data 'undefined' does not match format '%Y-%m-%d'
2025-06-06 20:50:19,531 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:50:19,539 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:50:47,159 INFO: 取消发布周菜单成功: id=32 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:571]
2025-06-06 20:51:07,181 INFO: 发布周菜单成功: id=32 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:537]
2025-06-06 20:51:07,187 INFO: 更新了日期 2025-05-26 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:51:07,193 INFO: 更新了日期 2025-05-27 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:51:07,198 INFO: 更新了日期 2025-05-28 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:51:07,205 INFO: 更新了日期 2025-05-29 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:51:07,210 INFO: 更新了日期 2025-05-30 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:51:07,214 INFO: 更新了日期 2025-05-31 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:51:07,221 INFO: 更新了日期 2025-06-01 的工作日志菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\menu_sync_service.py:150]
2025-06-06 20:51:18,651 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:51:18,665 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:52:01,701 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 20:52:08,858 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 20:52:08,875 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:421]
2025-06-06 20:52:08,890 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:300]
2025-06-06 20:52:08,894 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 20:54:53,949 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 20:56:46,660 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 20:57:57,474 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
