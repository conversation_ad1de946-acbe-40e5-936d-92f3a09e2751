2025-06-06 21:01:38,447 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:421]
2025-06-06 21:03:39,501 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 21:04:47,437 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
