2025-06-06 21:22:35,096 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (42,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 21:25:10,702 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 21:25:11,029 ERROR: 获取供应链状态失败: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'stock_in_records' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: ('2025-06-06', 22)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 21:25:11,032 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42S02', "[42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]对象名 'inventory' 无效。 (208) (SQLExecDirectW); [42S02] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)")
[SQL: 
            SELECT TOP 3 i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:423]
2025-06-06 21:28:42,950 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-06 21:30:17,615 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 21:30:17,623 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 21:30:17,624 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 42)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 21:30:17,632 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (42,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 21:32:02,218 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 21:32:02,222 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 21:32:02,223 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 21:32:02,234 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 21:32:10,066 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 21:32:10,072 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 21:32:10,073 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 21:32:10,085 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 21:32:44,142 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 21:46:53,802 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
