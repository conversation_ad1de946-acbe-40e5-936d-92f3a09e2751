2025-06-04 18:08:40,324 INFO: 使用优化后的查询: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-04 18:08:40,339 INFO: 执行主SQL查询: area_id=44, week_start_str=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-04 18:08:40,339 INFO: 主查询成功，找到菜单: id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:97]
2025-06-04 18:08:40,339 INFO: 已存在该周的菜单: id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:208]
2025-06-04 18:08:40,339 INFO: 周菜单创建成功: id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-04 18:08:40,339 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 42, 'status': '计划中'} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-04 18:08:44,468 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-09"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 18:08:44,468 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 18:08:44,468 INFO: 检查用户权限: user_id=36, area_id=44 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-04 18:08:44,468 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 18:08:44,468 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 18:08:44,468 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 18:08:44,468 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-04 18:08:44,468 INFO: 转换后的日期对象: 2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-04 18:08:44,468 INFO: 计算的周结束日期: 2025-06-15 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-04 18:08:44,468 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-04 18:08:44,468 INFO: 获取周菜单: area_id=44, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-04 18:08:44,468 INFO: 从缓存获取菜单: menu_44_2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:61]
2025-06-04 18:08:44,468 INFO: 已存在该周的菜单: id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:208]
2025-06-04 18:08:44,468 INFO: 周菜单创建成功: id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-04 18:08:44,468 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 42, 'status': '计划中'} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-04 18:09:47,939 INFO: 获取副表数据用于补全主表: weekly_menu_id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:342]
2025-06-04 18:09:47,939 INFO: 副表数据映射构建完成: 0 天, 0 个菜品 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:370]
2025-06-04 18:09:47,939 INFO: 主表数据补全完成，准备保存: 总菜品数=12, 已补全=12, 未补全=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:419]
2025-06-04 18:09:47,939 INFO: 删除现有菜单食谱(主表): weekly_menu_id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:428]
2025-06-04 18:09:47,939 INFO: 删除现有菜单食谱(副表): weekly_menu_id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:437]
2025-06-04 18:09:47,955 INFO: 保存周菜单成功(主表和副表): id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-04 18:09:47,955 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 18:18:35,212 WARNING: Suspicious User-Agent blocked: mozilla/5.0 applewebkit/537.36 (khtml, like gecko; compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm) chrome/116.0.1938.76 safari/537.36 from 52.167.144.66 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:103]
2025-06-04 18:18:35,212 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:18:38,965 WARNING: Suspicious User-Agent blocked: mozilla/5.0 applewebkit/537.36 (khtml, like gecko; compatible; bingbot/2.0; +http://www.bing.com/bingbot.htm) chrome/116.0.1938.76 safari/537.36 from 157.55.39.200 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:103]
2025-06-04 18:18:38,965 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:25:14,973 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 18:25:14,973 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 18:25:29,343 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 18:25:33,510 WARNING: Suspicious path blocked: /admin/dashboard from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:25:33,525 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:25:35,802 WARNING: Suspicious path blocked: /admin/settings from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:25:35,802 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:25:40,024 WARNING: Suspicious path blocked: /admin/dashboard from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:25:40,024 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:25:42,641 WARNING: Suspicious path blocked: /admin/guide/dashboard from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:25:42,647 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:25:44,640 WARNING: Suspicious path blocked: /admin/guide/videos from 124.229.112.146 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:25:44,640 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:27:13,260 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
