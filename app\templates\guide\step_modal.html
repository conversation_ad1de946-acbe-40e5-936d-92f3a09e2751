<!-- 用户引导步骤模态框 -->
<div class="modal fade" id="guideStepModal" tabindex="-1" role="dialog" aria-labelledby="guideStepModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="guideStepModalLabel">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    <span id="stepTitle">系统引导</span>
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="stepContent">
                <!-- 动态内容将在这里加载 -->
            </div>
            <div class="modal-footer">
                <div class="progress w-100 mb-2" style="height: 8px;">
                    <div class="progress-bar bg-success" role="progressbar" id="guideProgress" style="width: 0%"></div>
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-secondary" id="skipGuideBtn">
                            <i class="fas fa-times mr-1"></i>跳过引导
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary mr-2" id="prevStepBtn" style="display: none;">
                            <i class="fas fa-arrow-left mr-1"></i>上一步
                        </button>
                        <button type="button" class="btn btn-primary" id="nextStepBtn">
                            <i class="fas fa-arrow-right ml-1"></i>下一步
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引导步骤内容模板 -->

<!-- 欢迎步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="welcomeStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-utensils fa-4x text-primary mb-3"></i>
        <h4>欢迎使用校园餐智慧食堂平台！</h4>
        <p class="text-muted">让我们一起了解完整的食堂管理流程</p>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <h6><i class="fas fa-star text-warning mr-2"></i>核心特色</h6>
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success mr-2"></i>完整的食堂管理工作流程</li>
                <li><i class="fas fa-check text-success mr-2"></i>从采购到餐桌的全程追溯</li>
                <li><i class="fas fa-check text-success mr-2"></i>专业的PDF报表生成</li>
                <li><i class="fas fa-check text-success mr-2"></i>移动端二维码检查</li>
                <li><i class="fas fa-check text-success mr-2"></i>智能化数据分析</li>
            </ul>
        </div>
        <div class="col-md-6">
            <h6><i class="fas fa-route text-info mr-2"></i>学习路径</h6>
            <div class="timeline-sm">
                <div class="timeline-item">
                    <span class="timeline-marker bg-primary"></span>
                    <span class="timeline-content">日常管理模块</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-info"></span>
                    <span class="timeline-content">供应商管理</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-success"></span>
                    <span class="timeline-content">食材食谱管理</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-warning"></span>
                    <span class="timeline-content">采购到出库流程</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-danger"></span>
                    <span class="timeline-content">溯源与留样</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info mt-3">
        <i class="fas fa-lightbulb mr-2"></i>
        <strong>提示：</strong>整个引导过程大约需要15-20分钟，您可以随时暂停或跳过。
    </div>
</script>

<!-- 日常管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="daily_managementStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-clipboard-list fa-3x text-primary mb-3"></i>
        <h4>食堂日常管理模块</h4>
        <p class="text-muted">6个核心功能模块，整合资料生成PDF报告</p>
    </div>
    
    <div class="row">
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-primary">
                <div class="card-body text-center">
                    <i class="fas fa-search fa-2x text-primary mb-2"></i>
                    <h6>检查记录</h6>
                    <p class="small text-muted">食品安全检查，支持二维码扫描上传</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-info">
                <div class="card-body text-center">
                    <i class="fas fa-users fa-2x text-info mb-2"></i>
                    <h6>陪餐记录</h6>
                    <p class="small text-muted">陪餐人员记录，生成陪餐报告</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-success">
                <div class="card-body text-center">
                    <i class="fas fa-graduation-cap fa-2x text-success mb-2"></i>
                    <h6>培训记录</h6>
                    <p class="small text-muted">员工培训档案管理</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-warning">
                <div class="card-body text-center">
                    <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                    <h6>特殊事件</h6>
                    <p class="small text-muted">突发事件记录处理</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-danger">
                <div class="card-body text-center">
                    <i class="fas fa-bug fa-2x text-danger mb-2"></i>
                    <h6>问题记录</h6>
                    <p class="small text-muted">问题发现与整改跟踪</p>
                </div>
            </div>
        </div>
        <div class="col-md-4 mb-3">
            <div class="card h-100 border-secondary">
                <div class="card-body text-center">
                    <i class="fas fa-calendar-check fa-2x text-secondary mb-2"></i>
                    <h6>工作日志</h6>
                    <p class="small text-muted">日常工作记录，生成工作报告</p>
                </div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-success">
        <h6><i class="fas fa-star mr-2"></i>核心亮点</h6>
        <ul class="mb-0">
            <li><strong>二维码检查：</strong>生成学校专属二维码，员工扫码上传检查数据</li>
            <li><strong>PDF报告：</strong>一键生成专业PDF报告，方便各部门检查</li>
            <li><strong>资料整合：</strong>整合相关资料，形成完整的管理档案</li>
        </ul>
    </div>
    
    <!-- 视频演示区域 -->
    <div class="card mt-3" id="videoSection_daily_management">
        <div class="card-header bg-primary text-white">
            <h6 class="mb-0">
                <i class="fas fa-video mr-2"></i>视频演示
                <span class="badge badge-light text-primary ml-2" id="videoCount_daily_management">加载中...</span>
            </h6>
        </div>
        <div class="card-body" id="videoList_daily_management">
            <div class="text-center">
                <div class="spinner-border spinner-border-sm text-primary" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2 mb-0 text-muted">正在加载视频资源...</p>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('daily_management.index') }}" class="btn btn-primary mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>体验日常管理
        </a>
        <button class="btn btn-outline-primary" data-onclick="generateQRCode()">
            <i class="fas fa-qrcode mr-1"></i>生成检查二维码
        </button>
    </div>
</script>

<!-- 供应商管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="suppliersStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-truck fa-3x text-success mb-3"></i>
        <h4>供应商管理</h4>
        <p class="text-muted">建立合格供应商档案，确保食材来源可靠</p>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <h6><i class="fas fa-importance text-warning mr-2"></i>重要性</h6>
            <ul class="list-unstyled">
                <li><i class="fas fa-shield-alt text-success mr-2"></i>建立合格供应商档案</li>
                <li><i class="fas fa-check-circle text-success mr-2"></i>确保食材来源可靠</li>
                <li><i class="fas fa-clipboard-check text-success mr-2"></i>规范采购流程</li>
                <li><i class="fas fa-star text-success mr-2"></i>建立供应商评价体系</li>
            </ul>
        </div>
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-eye mr-2"></i>演示数据预览</h6>
                </div>
                <div class="card-body">
                    <p><strong>供应商：</strong>绿色农场有限公司</p>
                    <p><strong>联系人：</strong>张经理</p>
                    <p><strong>电话：</strong>13800138000</p>
                    <p><strong>主营：</strong>新鲜蔬菜、有机水果、绿色大米</p>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 视频演示区域 -->
    <div class="card mt-3" id="videoSection_suppliers">
        <div class="card-header bg-success text-white">
            <h6 class="mb-0">
                <i class="fas fa-video mr-2"></i>视频演示
                <span class="badge badge-light text-success ml-2" id="videoCount_suppliers">加载中...</span>
            </h6>
        </div>
        <div class="card-body" id="videoList_suppliers">
            <div class="text-center">
                <div class="spinner-border spinner-border-sm text-success" role="status">
                    <span class="sr-only">加载中...</span>
                </div>
                <p class="mt-2 mb-0 text-muted">正在加载视频资源...</p>
            </div>
        </div>
    </div>

    <div class="text-center mt-4">
        <button class="btn btn-success mr-2" data-onclick="createDemoSupplier()">
            <i class="fas fa-plus mr-1"></i>创建演示供应商
        </button>
        <a href="{{ url_for('supplier.index') }}" class="btn btn-outline-success" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>查看供应商管理
        </a>
    </div>
</script>

<style nonce="{{ csp_nonce }}">
.timeline-sm {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    margin-left: 10px;
    font-size: 0.9rem;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 12px;
    width: 2px;
    height: 20px;
    background-color: #dee2e6;
}

/* 视频相关样式 */
.video-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: 0.25rem;
}

.video-thumbnail:hover .video-overlay {
    opacity: 1;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* 视频模态框样式 */
#videoModal .modal-dialog {
    max-width: 90%;
}

#videoModal video {
    width: 100%;
    height: auto;
    max-height: 70vh;
}

/* 视频加载动画 */
.video-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100px;
}

/* 视频卡片悬停效果 */
.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>