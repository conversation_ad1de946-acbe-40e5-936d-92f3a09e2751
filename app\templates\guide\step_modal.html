<!-- 用户引导步骤模态框 -->
<div class="modal fade" id="guideStepModal" tabindex="-1" role="dialog" aria-labelledby="guideStepModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="guideStepModalLabel">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    <span id="stepTitle">系统引导</span>
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="stepContent">
                <!-- 动态内容将在这里加载 -->
            </div>
            <div class="modal-footer">
                <div class="progress w-100 mb-2" style="height: 8px;">
                    <div class="progress-bar bg-success" role="progressbar" id="guideProgress" style="width: 0%"></div>
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-secondary" id="skipGuideBtn">
                            <i class="fas fa-times mr-1"></i>跳过引导
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary mr-2" id="prevStepBtn" style="display: none;">
                            <i class="fas fa-arrow-left mr-1"></i>上一步
                        </button>
                        <button type="button" class="btn btn-primary" id="nextStepBtn">
                            <i class="fas fa-arrow-right ml-1"></i>下一步
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引导步骤内容模板 -->

<!-- 欢迎步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="welcomeStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-utensils fa-4x text-primary mb-3"></i>
        <h4>欢迎使用校园餐智慧食堂平台！</h4>
        <p class="text-muted">让我们一起了解完整的食堂管理流程</p>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <h6><i class="fas fa-star text-warning mr-2"></i>核心特色</h6>
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success mr-2"></i>完整的食堂管理工作流程</li>
                <li><i class="fas fa-check text-success mr-2"></i>从采购到餐桌的全程追溯</li>
                <li><i class="fas fa-check text-success mr-2"></i>专业的PDF报表生成</li>
                <li><i class="fas fa-check text-success mr-2"></i>移动端二维码检查</li>
                <li><i class="fas fa-check text-success mr-2"></i>智能化数据分析</li>
            </ul>
        </div>
        <div class="col-md-6">
            <h6><i class="fas fa-route text-info mr-2"></i>学习路径</h6>
            <div class="timeline-sm">
                <div class="timeline-item">
                    <span class="timeline-marker bg-primary"></span>
                    <span class="timeline-content">日常管理模块</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-info"></span>
                    <span class="timeline-content">供应商管理</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-success"></span>
                    <span class="timeline-content">食材食谱管理</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-warning"></span>
                    <span class="timeline-content">采购到出库流程</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-danger"></span>
                    <span class="timeline-content">溯源与留样</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info mt-3">
        <i class="fas fa-lightbulb mr-2"></i>
        <strong>提示：</strong>整个引导过程大约需要15-20分钟，您可以随时暂停或跳过。
    </div>
</script>

<!-- 日常管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="daily_managementStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-clipboard-list fa-3x text-primary mb-3"></i>
        <h4>食堂日常管理模块</h4>
        <p class="text-muted">6个核心功能模块，整合资料生成PDF报告</p>
    </div>

    <!-- 视频教学区域 -->
    <div class="row mb-4">
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card h-100 border-primary">
                        <div class="card-body text-center">
                            <i class="fas fa-search fa-2x text-primary mb-2"></i>
                            <h6>检查记录</h6>
                            <p class="small text-muted">食品安全检查，支持二维码扫描上传</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100 border-info">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-2x text-info mb-2"></i>
                            <h6>陪餐记录</h6>
                            <p class="small text-muted">陪餐人员记录，生成陪餐报告</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100 border-success">
                        <div class="card-body text-center">
                            <i class="fas fa-graduation-cap fa-2x text-success mb-2"></i>
                            <h6>培训记录</h6>
                            <p class="small text-muted">员工培训档案管理</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100 border-warning">
                        <div class="card-body text-center">
                            <i class="fas fa-exclamation-triangle fa-2x text-warning mb-2"></i>
                            <h6>特殊事件</h6>
                            <p class="small text-muted">突发事件记录处理</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100 border-danger">
                        <div class="card-body text-center">
                            <i class="fas fa-bug fa-2x text-danger mb-2"></i>
                            <h6>问题记录</h6>
                            <p class="small text-muted">问题发现与整改跟踪</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card h-100 border-secondary">
                        <div class="card-body text-center">
                            <i class="fas fa-calendar-check fa-2x text-secondary mb-2"></i>
                            <h6>工作日志</h6>
                            <p class="small text-muted">日常工作记录，生成工作报告</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card h-100 border-primary">
                <div class="card-header bg-primary text-white text-center">
                    <h6 class="mb-0">
                        <i class="fas fa-play-circle mr-2"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_daily_management">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="alert alert-success">
        <h6><i class="fas fa-star mr-2"></i>核心亮点</h6>
        <ul class="mb-0">
            <li><strong>二维码检查：</strong>生成学校专属二维码，员工扫码上传检查数据</li>
            <li><strong>PDF报告：</strong>一键生成专业PDF报告，方便各部门检查</li>
            <li><strong>资料整合：</strong>整合相关资料，形成完整的管理档案</li>
        </ul>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('daily_management.index') }}" class="btn btn-primary mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>体验日常管理
        </a>
        <button class="btn btn-outline-primary" data-onclick="generateQRCode()">
            <i class="fas fa-qrcode mr-1"></i>生成检查二维码
        </button>
    </div>
</script>

<!-- 供应商管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="suppliersStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-truck fa-3x text-success mb-3"></i>
        <h4>供应商管理</h4>
        <p class="text-muted">建立合格供应商档案，确保食材来源可靠</p>
    </div>

    <div class="row mb-4">
        <div class="col-md-5">
            <h6><i class="fas fa-star text-warning mr-2"></i>管理要点</h6>
            <ul class="list-unstyled">
                <li><i class="fas fa-shield-alt text-success mr-2"></i>建立合格供应商档案</li>
                <li><i class="fas fa-check-circle text-success mr-2"></i>确保食材来源可靠</li>
                <li><i class="fas fa-clipboard-check text-success mr-2"></i>规范采购流程</li>
                <li><i class="fas fa-star text-success mr-2"></i>建立供应商评价体系</li>
            </ul>

            <div class="card mt-3">
                <div class="card-header bg-light">
                    <h6 class="mb-0"><i class="fas fa-eye mr-2"></i>演示数据预览</h6>
                </div>
                <div class="card-body">
                    <p class="mb-1"><strong>供应商：</strong>绿色农场有限公司</p>
                    <p class="mb-1"><strong>联系人：</strong>张经理</p>
                    <p class="mb-1"><strong>电话：</strong>13800138000</p>
                    <p class="mb-0"><strong>主营：</strong>新鲜蔬菜、有机水果、绿色大米</p>
                </div>
            </div>
        </div>

        <div class="col-md-7">
            <div class="card h-100 border-success">
                <div class="card-header bg-success text-white text-center">
                    <h6 class="mb-0">
                        <i class="fas fa-play-circle mr-2"></i>操作演示教学
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_suppliers">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-success" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center">
        <button class="btn btn-success mr-2" data-onclick="createDemoSupplier()">
            <i class="fas fa-plus mr-1"></i>创建演示供应商
        </button>
        <a href="{{ url_for('supplier.index') }}" class="btn btn-outline-success" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>查看供应商管理
        </a>
    </div>
</script>

<!-- 食材食谱管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="ingredients_recipesStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-leaf fa-3x text-info mb-3"></i>
        <h4>食材食谱管理</h4>
        <p class="text-muted">建立食材档案，制定营养食谱，控制成本</p>
    </div>

    <div class="row mb-4">
        <div class="col-md-6">
            <div class="card h-100">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0"><i class="fas fa-carrot mr-2"></i>食材档案管理</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success mr-2"></i>建立完整食材基础档案</li>
                        <li><i class="fas fa-check text-success mr-2"></i>设置营养成分信息</li>
                        <li><i class="fas fa-check text-success mr-2"></i>管理食材分类和单位</li>
                        <li><i class="fas fa-check text-success mr-2"></i>设置采购价格参考</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card h-100 border-info">
                <div class="card-header bg-info text-white text-center">
                    <h6 class="mb-0">
                        <i class="fas fa-play-circle mr-2"></i>操作演示教学
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_ingredients_recipes">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-info" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-3">
        <div class="col-md-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0"><i class="fas fa-utensils mr-2"></i>食谱制作</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success mr-2"></i>营养搭配科学合理</li>
                        <li><i class="fas fa-check text-success mr-2"></i>自动计算成本价格</li>
                        <li><i class="fas fa-check text-success mr-2"></i>支持制作工艺说明</li>
                        <li><i class="fas fa-check text-success mr-2"></i>食谱图片展示</li>
                    </ul>
                </div>
            </div>
        </div>
        <div class="col-md-6">
            <div class="card border-success">
                <div class="card-header bg-success text-white">
                    <h6 class="mb-0"><i class="fas fa-calculator mr-2"></i>成本控制</h6>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled mb-0">
                        <li><i class="fas fa-check text-success mr-2"></i>实时计算食谱成本</li>
                        <li><i class="fas fa-check text-success mr-2"></i>营养成分自动汇总</li>
                        <li><i class="fas fa-check text-success mr-2"></i>用量精确到克</li>
                        <li><i class="fas fa-check text-success mr-2"></i>成本分析报表</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center">
        <a href="{{ url_for('ingredient.index') }}" class="btn btn-info mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>食材管理
        </a>
        <a href="{{ url_for('recipe.index') }}" class="btn btn-outline-info" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>食谱管理
        </a>
    </div>
</script>

<!-- 周菜单制定步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="weekly_menuStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-calendar-alt fa-3x text-warning mb-3"></i>
        <h4>周菜单制定</h4>
        <p class="text-muted">科学制定周菜单，营养搭配合理，成本控制精准</p>
    </div>

    <div class="row mb-4">
        <div class="col-md-5">
            <div class="card">
                <div class="card-header bg-warning text-white">
                    <h6 class="mb-0"><i class="fas fa-list-alt mr-2"></i>制定流程</h6>
                </div>
                <div class="card-body">
                    <div class="timeline-sm">
                        <div class="timeline-item">
                            <span class="timeline-marker bg-primary"></span>
                            <div class="timeline-content">
                                <strong>创建菜单计划</strong><br>
                                <small class="text-muted">设置周期和基本信息</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <span class="timeline-marker bg-info"></span>
                            <div class="timeline-content">
                                <strong>安排具体菜品</strong><br>
                                <small class="text-muted">拖拽式编辑，直观便捷</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <span class="timeline-marker bg-success"></span>
                            <div class="timeline-content">
                                <strong>营养成本分析</strong><br>
                                <small class="text-muted">自动计算营养和成本</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <span class="timeline-marker bg-warning"></span>
                            <div class="timeline-content">
                                <strong>发布执行</strong><br>
                                <small class="text-muted">生成采购计划</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-7">
            <div class="card h-100 border-warning">
                <div class="card-header bg-warning text-white text-center">
                    <h6 class="mb-0">
                        <i class="fas fa-play-circle mr-2"></i>操作演示教学
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_weekly_menu">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-warning" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="alert alert-warning">
        <h6><i class="fas fa-lightbulb mr-2"></i>智能特色</h6>
        <div class="row">
            <div class="col-md-6">
                <ul class="mb-0">
                    <li><strong>拖拽编辑：</strong>直观的菜品安排界面</li>
                    <li><strong>营养分析：</strong>自动计算营养成分</li>
                </ul>
            </div>
            <div class="col-md-6">
                <ul class="mb-0">
                    <li><strong>成本控制：</strong>实时显示成本信息</li>
                    <li><strong>采购联动：</strong>一键生成采购计划</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="text-center">
        <a href="{{ url_for('weekly_menu.index') }}" class="btn btn-warning mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>周菜单管理
        </a>
        <button class="btn btn-outline-warning" data-onclick="createDemoWeeklyMenu()">
            <i class="fas fa-plus mr-1"></i>创建演示菜单
        </button>
    </div>
</script>

<style nonce="{{ csp_nonce }}">
.timeline-sm {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    margin-left: 10px;
    font-size: 0.9rem;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 12px;
    width: 2px;
    height: 20px;
    background-color: #dee2e6;
}

/* 视频相关样式 */
.video-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: 0.25rem;
}

.video-thumbnail:hover .video-overlay {
    opacity: 1;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* 视频模态框样式 */
#videoModal .modal-dialog {
    max-width: 90%;
}

#videoModal video {
    width: 100%;
    height: auto;
    max-height: 70vh;
}

/* 视频加载动画 */
.video-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    padding: 20px;
}

/* 视频卡片悬停效果 */
.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

/* 紧凑按钮样式 */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.2rem;
}

/* 视频列表滚动条样式 */
.video-list-scroll::-webkit-scrollbar {
    width: 4px;
}

.video-list-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.video-list-scroll::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 2px;
}

.video-list-scroll::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 引导页面专用的紧凑布局 */
.guide-video-container {
    max-height: 280px;
    overflow-y: auto;
}

.guide-video-item {
    transition: background-color 0.2s ease;
}

.guide-video-item:hover {
    background-color: rgba(0,0,0,0.02);
    border-radius: 0.25rem;
}

/* 时间轴样式优化 */
.timeline-sm .timeline-marker {
    background-color: #fff;
    border: 2px solid #dee2e6;
    box-shadow: 0 0 0 2px #fff, 0 0 0 4px #dee2e6;
}
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>