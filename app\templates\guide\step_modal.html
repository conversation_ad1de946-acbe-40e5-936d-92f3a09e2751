<!-- 用户引导步骤模态框 -->
<div class="modal fade" id="guideStepModal" tabindex="-1" role="dialog" aria-labelledby="guideStepModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="guideStepModalLabel">
                    <i class="fas fa-graduation-cap mr-2"></i>
                    <span id="stepTitle">系统引导</span>
                </h5>
                <button type="button" class="close text-white" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
            </div>
            <div class="modal-body" id="stepContent">
                <!-- 动态内容将在这里加载 -->
            </div>
            <div class="modal-footer">
                <div class="progress w-100 mb-2" style="height: 8px;">
                    <div class="progress-bar bg-success" role="progressbar" id="guideProgress" style="width: 0%"></div>
                </div>
                <div class="w-100 d-flex justify-content-between">
                    <div>
                        <button type="button" class="btn btn-outline-secondary" id="skipGuideBtn">
                            <i class="fas fa-times mr-1"></i>跳过引导
                        </button>
                    </div>
                    <div>
                        <button type="button" class="btn btn-secondary mr-2" id="prevStepBtn" style="display: none;">
                            <i class="fas fa-arrow-left mr-1"></i>上一步
                        </button>
                        <button type="button" class="btn btn-primary" id="nextStepBtn">
                            <i class="fas fa-arrow-right ml-1"></i>下一步
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 引导步骤内容模板 -->

<!-- 欢迎步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="welcomeStepTemplate">
    <div class="text-center mb-4">
        <i class="fas fa-utensils fa-4x text-primary mb-3"></i>
        <h4>欢迎使用校园餐智慧食堂平台！</h4>
        <p class="text-muted">让我们一起了解完整的食堂管理流程</p>
    </div>
    
    <div class="row">
        <div class="col-md-6">
            <h6><i class="fas fa-star text-warning mr-2"></i>核心特色</h6>
            <ul class="list-unstyled">
                <li><i class="fas fa-check text-success mr-2"></i>完整的食堂管理工作流程</li>
                <li><i class="fas fa-check text-success mr-2"></i>从采购到餐桌的全程追溯</li>
                <li><i class="fas fa-check text-success mr-2"></i>专业的PDF报表生成</li>
                <li><i class="fas fa-check text-success mr-2"></i>移动端二维码检查</li>
                <li><i class="fas fa-check text-success mr-2"></i>智能化数据分析</li>
            </ul>
        </div>
        <div class="col-md-6">
            <h6><i class="fas fa-route text-info mr-2"></i>学习路径</h6>
            <div class="timeline-sm">
                <div class="timeline-item">
                    <span class="timeline-marker bg-primary"></span>
                    <span class="timeline-content">日常管理模块</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-info"></span>
                    <span class="timeline-content">供应商管理</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-success"></span>
                    <span class="timeline-content">食材食谱管理</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-warning"></span>
                    <span class="timeline-content">采购到出库流程</span>
                </div>
                <div class="timeline-item">
                    <span class="timeline-marker bg-danger"></span>
                    <span class="timeline-content">溯源与留样</span>
                </div>
            </div>
        </div>
    </div>
    
    <div class="alert alert-info mt-3">
        <i class="fas fa-lightbulb mr-2"></i>
        <strong>提示：</strong>整个引导过程大约需要15-20分钟，您可以随时暂停或跳过。
    </div>
</script>

<!-- 日常管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="daily_managementStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-clipboard-list fa-2x text-primary mb-2"></i>
        <h4 class="mb-1">食堂日常管理模块</h4>
        <p class="text-muted small mb-0">6个核心功能模块，整合资料生成PDF报告</p>
    </div>

    <!-- 主要内容区域 -->
    <div class="row">
        <!-- 功能模块区域 -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-4 col-sm-6 mb-2">
                    <div class="card border-primary" style="min-height: 100px;">
                        <div class="card-body text-center py-2">
                            <i class="fas fa-search text-primary mb-1" style="font-size: 1.2rem;"></i>
                            <h6 class="mb-1" style="font-size: 0.9rem;">检查记录</h6>
                            <p class="small text-muted mb-0" style="font-size: 0.75rem;">食品安全检查，支持二维码扫描上传</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6 mb-2">
                    <div class="card border-info" style="min-height: 100px;">
                        <div class="card-body text-center py-2">
                            <i class="fas fa-users text-info mb-1" style="font-size: 1.2rem;"></i>
                            <h6 class="mb-1" style="font-size: 0.9rem;">陪餐记录</h6>
                            <p class="small text-muted mb-0" style="font-size: 0.75rem;">陪餐人员记录，生成陪餐报告</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6 mb-2">
                    <div class="card border-success" style="min-height: 100px;">
                        <div class="card-body text-center py-2">
                            <i class="fas fa-graduation-cap text-success mb-1" style="font-size: 1.2rem;"></i>
                            <h6 class="mb-1" style="font-size: 0.9rem;">培训记录</h6>
                            <p class="small text-muted mb-0" style="font-size: 0.75rem;">员工培训档案管理</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6 mb-2">
                    <div class="card border-warning" style="min-height: 100px;">
                        <div class="card-body text-center py-2">
                            <i class="fas fa-exclamation-triangle text-warning mb-1" style="font-size: 1.2rem;"></i>
                            <h6 class="mb-1" style="font-size: 0.9rem;">特殊事件</h6>
                            <p class="small text-muted mb-0" style="font-size: 0.75rem;">突发事件记录处理</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6 mb-2">
                    <div class="card border-danger" style="min-height: 100px;">
                        <div class="card-body text-center py-2">
                            <i class="fas fa-bug text-danger mb-1" style="font-size: 1.2rem;"></i>
                            <h6 class="mb-1" style="font-size: 0.9rem;">问题记录</h6>
                            <p class="small text-muted mb-0" style="font-size: 0.75rem;">问题发现与整改跟踪</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 col-sm-6 mb-2">
                    <div class="card border-secondary" style="min-height: 100px;">
                        <div class="card-body text-center py-2">
                            <i class="fas fa-calendar-check text-secondary mb-1" style="font-size: 1.2rem;"></i>
                            <h6 class="mb-1" style="font-size: 0.9rem;">工作日志</h6>
                            <p class="small text-muted mb-0" style="font-size: 0.75rem;">日常工作记录，生成工作报告</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频教学区域 -->
        <div class="col-md-4">
            <div class="card border-primary h-100">
                <div class="card-header bg-primary text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_daily_management">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 核心亮点 -->
    <div class="alert alert-success mt-3 py-2">
        <h6 class="mb-2" style="font-size: 0.9rem;"><i class="fas fa-star mr-2"></i>核心亮点</h6>
        <div class="row">
            <div class="col-md-4">
                <small><strong>二维码检查：</strong>生成学校专属二维码，员工扫码上传检查数据</small>
            </div>
            <div class="col-md-4">
                <small><strong>PDF报告：</strong>一键生成专业PDF报告，方便各部门检查</small>
            </div>
            <div class="col-md-4">
                <small><strong>资料整合：</strong>整合相关资料，形成完整的管理档案</small>
            </div>
        </div>
    </div>

    <!-- 操作按钮 -->
    <div class="text-center mt-3">
        <a href="{{ url_for('daily_management.index') }}" class="btn btn-primary btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>体验日常管理
        </a>
        <button class="btn btn-outline-primary btn-sm" data-onclick="generateQRCode()">
            <i class="fas fa-qrcode mr-1"></i>生成检查二维码
        </button>
    </div>
</script>

<!-- 供应商管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="suppliersStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-truck fa-2x text-success mb-2"></i>
        <h4 class="mb-1">供应商管理</h4>
        <p class="text-muted small mb-0">建立合格供应商档案，确保食材来源可靠</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <h6 class="mb-2" style="font-size: 0.9rem;"><i class="fas fa-star text-warning mr-1"></i>管理要点</h6>
            <ul class="list-unstyled mb-3" style="font-size: 0.85rem;">
                <li class="mb-1"><i class="fas fa-shield-alt text-success mr-2"></i>建立合格供应商档案</li>
                <li class="mb-1"><i class="fas fa-check-circle text-success mr-2"></i>确保食材来源可靠</li>
                <li class="mb-1"><i class="fas fa-clipboard-check text-success mr-2"></i>规范采购流程</li>
                <li class="mb-1"><i class="fas fa-star text-success mr-2"></i>建立供应商评价体系</li>
            </ul>

            <div class="card">
                <div class="card-header bg-light py-2">
                    <h6 class="mb-0" style="font-size: 0.85rem;"><i class="fas fa-eye mr-1"></i>演示数据预览</h6>
                </div>
                <div class="card-body py-2">
                    <p class="mb-1 small"><strong>供应商：</strong>绿色农场有限公司</p>
                    <p class="mb-1 small"><strong>联系人：</strong>张经理</p>
                    <p class="mb-1 small"><strong>电话：</strong>13800138000</p>
                    <p class="mb-0 small"><strong>主营：</strong>新鲜蔬菜、有机水果、绿色大米</p>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-success h-100">
                <div class="card-header bg-success text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示教学
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_suppliers">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-success" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <button class="btn btn-success btn-sm mr-2" data-onclick="createDemoSupplier()">
            <i class="fas fa-plus mr-1"></i>创建演示供应商
        </button>
        <a href="{{ url_for('supplier.index') }}" class="btn btn-outline-success btn-sm" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>查看供应商管理
        </a>
    </div>
</script>

<!-- 食材食谱管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="ingredients_recipesStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-leaf fa-2x text-info mb-2"></i>
        <h4 class="mb-1">食材食谱管理</h4>
        <p class="text-muted small mb-0">建立食材档案，制定营养食谱，控制成本</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card border-info h-100">
                <div class="card-header bg-info text-white py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-carrot mr-1"></i>食材档案管理</h6>
                </div>
                <div class="card-body py-2">
                    <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>建立完整食材基础档案</li>
                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>设置营养成分信息</li>
                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>管理食材分类和单位</li>
                        <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>设置采购价格参考</li>
                    </ul>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-info h-100">
                <div class="card-header bg-info text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示教学
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_ingredients_recipes">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-info" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 可收缩的详细功能区域 -->
    <div class="mt-3">
        <div class="card border-light">
            <div class="card-header bg-light py-2" style="cursor: pointer;" data-toggle="collapse" data-target="#ingredientsDetailsCollapse" aria-expanded="false">
                <h6 class="mb-0" style="font-size: 0.9rem;">
                    <i class="fas fa-chevron-down mr-2"></i>
                    <i class="fas fa-info-circle text-info mr-1"></i>详细功能介绍
                    <small class="text-muted ml-2">(点击展开/收起)</small>
                </h6>
            </div>
            <div class="collapse" id="ingredientsDetailsCollapse">
                <div class="card-body py-2">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card border-warning">
                                <div class="card-header bg-warning text-white py-2">
                                    <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-utensils mr-1"></i>食谱制作</h6>
                                </div>
                                <div class="card-body py-2">
                                    <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>营养搭配科学合理</li>
                                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>自动计算成本价格</li>
                                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>支持制作工艺说明</li>
                                        <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>食谱图片展示</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card border-success">
                                <div class="card-header bg-success text-white py-2">
                                    <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-calculator mr-1"></i>成本控制</h6>
                                </div>
                                <div class="card-body py-2">
                                    <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>实时计算食谱成本</li>
                                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>营养成分自动汇总</li>
                                        <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>用量精确到克</li>
                                        <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>成本分析报表</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('ingredient.index') }}" class="btn btn-info btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>食材管理
        </a>
        <a href="{{ url_for('recipe.index') }}" class="btn btn-outline-info btn-sm" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>食谱管理
        </a>
    </div>
</script>

<!-- 周菜单制定步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="weekly_menuStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-calendar-alt fa-2x text-warning mb-2"></i>
        <h4 class="mb-1">周菜单制定</h4>
        <p class="text-muted small mb-0">科学制定周菜单，营养搭配合理，成本控制精准</p>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card border-warning">
                <div class="card-header bg-warning text-white py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-list-alt mr-1"></i>制定流程</h6>
                </div>
                <div class="card-body py-2">
                    <div class="timeline-sm">
                        <div class="timeline-item">
                            <span class="timeline-marker bg-primary"></span>
                            <div class="timeline-content">
                                <strong style="font-size: 0.85rem;">创建菜单计划</strong><br>
                                <small class="text-muted">设置周期和基本信息</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <span class="timeline-marker bg-info"></span>
                            <div class="timeline-content">
                                <strong style="font-size: 0.85rem;">安排具体菜品</strong><br>
                                <small class="text-muted">拖拽式编辑，直观便捷</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <span class="timeline-marker bg-success"></span>
                            <div class="timeline-content">
                                <strong style="font-size: 0.85rem;">营养成本分析</strong><br>
                                <small class="text-muted">自动计算营养和成本</small>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <span class="timeline-marker bg-warning"></span>
                            <div class="timeline-content">
                                <strong style="font-size: 0.85rem;">发布执行</strong><br>
                                <small class="text-muted">生成采购计划</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card border-warning h-100">
                <div class="card-header bg-warning text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示教学
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_weekly_menu">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-warning" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 可收缩的智能特色区域 -->
    <div class="mt-3">
        <div class="card border-light">
            <div class="card-header bg-light py-2" style="cursor: pointer;" data-toggle="collapse" data-target="#weeklyMenuFeaturesCollapse" aria-expanded="false">
                <h6 class="mb-0" style="font-size: 0.9rem;">
                    <i class="fas fa-chevron-down mr-2"></i>
                    <i class="fas fa-lightbulb text-warning mr-1"></i>智能特色功能
                    <small class="text-muted ml-2">(点击展开/收起)</small>
                </h6>
            </div>
            <div class="collapse" id="weeklyMenuFeaturesCollapse">
                <div class="card-body py-2">
                    <div class="alert alert-warning py-2 mb-0">
                        <div class="row">
                            <div class="col-md-6">
                                <ul class="mb-0" style="font-size: 0.85rem;">
                                    <li><strong>拖拽编辑：</strong>直观的菜品安排界面</li>
                                    <li><strong>营养分析：</strong>自动计算营养成分</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <ul class="mb-0" style="font-size: 0.85rem;">
                                    <li><strong>成本控制：</strong>实时显示成本信息</li>
                                    <li><strong>采购联动：</strong>一键生成采购计划</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('weekly_menu_v2.index') }}" class="btn btn-warning btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>周菜单管理
        </a>
        <button class="btn btn-outline-warning btn-sm" data-onclick="createDemoWeeklyMenu()">
            <i class="fas fa-plus mr-1"></i>创建演示菜单
        </button>
    </div>
</script>

<!-- 采购订单管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="purchase_orderStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-shopping-cart fa-2x text-danger mb-2"></i>
        <h4 class="mb-1">采购订单管理</h4>
        <p class="text-muted small mb-0">从周菜单生成采购单，规范采购流程，控制采购成本</p>
    </div>

    <div class="row">
        <!-- 功能介绍区域 -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-list mr-1"></i>采购计划</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>从周菜单自动生成</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>智能汇总食材需求</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>供应商价格比较</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>采购预算控制</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-clipboard-check mr-1"></i>订单执行</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>订单状态跟踪</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>供应商确认</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>交货时间管理</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>质量验收标准</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频演示区域 -->
        <div class="col-md-4">
            <div class="card border-danger h-100">
                <div class="card-header bg-danger text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_purchase_order">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-danger" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('purchase_order.index') }}" class="btn btn-danger btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>采购订单管理
        </a>
        <button class="btn btn-outline-danger btn-sm" data-onclick="createDemoPurchaseOrder()">
            <i class="fas fa-plus mr-1"></i>创建演示订单
        </button>
    </div>
</script>

<!-- 食材入库管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="stock_inStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-arrow-down fa-2x text-success mb-2"></i>
        <h4 class="mb-1">食材入库管理</h4>
        <p class="text-muted small mb-0">记录入库信息，建立溯源档案，确保食材质量</p>
    </div>

    <div class="row">
        <!-- 功能介绍区域 -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-clipboard-list mr-1"></i>入库登记</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>扫码快速入库</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>批次信息记录</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>质量检验记录</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>供应商信息关联</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-search-plus mr-1"></i>溯源建档</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>生成唯一批次号</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>记录来源信息</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>保质期管理</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>存储位置分配</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频演示区域 -->
        <div class="col-md-4">
            <div class="card border-success h-100">
                <div class="card-header bg-success text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_stock_in">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-success" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('stock_in.index') }}" class="btn btn-success btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>食材入库管理
        </a>
        <button class="btn btn-outline-success btn-sm" data-onclick="createDemoStockIn()">
            <i class="fas fa-plus mr-1"></i>创建演示入库
        </button>
    </div>
</script>

<!-- 消耗量计划步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="consumption_planStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-calculator fa-2x text-primary mb-2"></i>
        <h4 class="mb-1">消耗量计划</h4>
        <p class="text-muted small mb-0">制定食材使用计划，精确控制用量，避免浪费</p>
    </div>

    <div class="row">
        <!-- 功能介绍区域 -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-chart-line mr-1"></i>用量计算</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>基于菜单自动计算</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>考虑就餐人数</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>损耗率智能预估</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>安全库存提醒</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-clipboard-check mr-1"></i>计划执行</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>生成出库计划</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>分时段用料安排</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>实际用量记录</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>差异分析报告</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频演示区域 -->
        <div class="col-md-4">
            <div class="card border-primary h-100">
                <div class="card-header bg-primary text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_consumption_plan">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-primary" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('consumption_plan.index') }}" class="btn btn-primary btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>消耗量计划
        </a>
        <button class="btn btn-outline-primary btn-sm" data-onclick="createDemoConsumptionPlan()">
            <i class="fas fa-plus mr-1"></i>创建演示计划
        </button>
    </div>
</script>

<!-- 食材出库管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="stock_outStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-arrow-up fa-2x text-info mb-2"></i>
        <h4 class="mb-1">食材出库管理</h4>
        <p class="text-muted small mb-0">记录出库信息，完善溯源链条，确保用料准确</p>
    </div>

    <div class="row">
        <!-- 功能介绍区域 -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card border-info">
                        <div class="card-header bg-info text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-sign-out-alt mr-1"></i>出库操作</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>按计划批量出库</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>先进先出原则</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>实际用量记录</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>领料人员确认</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card border-secondary">
                        <div class="card-header bg-secondary text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-link mr-1"></i>溯源链条</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>记录使用去向</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>关联制作菜品</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>完善追溯档案</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>库存实时更新</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频演示区域 -->
        <div class="col-md-4">
            <div class="card border-info h-100">
                <div class="card-header bg-info text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_stock_out">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-info" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('stock_out.index') }}" class="btn btn-info btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>食材出库管理
        </a>
        <button class="btn btn-outline-info btn-sm" data-onclick="createDemoStockOut()">
            <i class="fas fa-plus mr-1"></i>创建演示出库
        </button>
    </div>
</script>

<!-- 食材溯源管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="traceabilityStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-search-plus fa-2x text-warning mb-2"></i>
        <h4 class="mb-1">食材溯源管理</h4>
        <p class="text-muted small mb-0">实现从采购到餐桌的全程追溯，确保食品安全</p>
    </div>

    <div class="row">
        <!-- 功能介绍区域 -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card border-warning">
                        <div class="card-header bg-warning text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-route mr-1"></i>追溯链条</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>供应商来源追溯</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>入库批次跟踪</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>使用去向记录</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>完整流向档案</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card border-danger">
                        <div class="card-header bg-danger text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-exclamation-triangle mr-1"></i>问题定位</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>快速问题定位</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>影响范围分析</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>召回处理支持</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>责任追究依据</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频演示区域 -->
        <div class="col-md-4">
            <div class="card border-warning h-100">
                <div class="card-header bg-warning text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_traceability">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-warning" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('traceability.index') }}" class="btn btn-warning btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>食材溯源管理
        </a>
        <button class="btn btn-outline-warning btn-sm" data-onclick="createDemoTraceability()">
            <i class="fas fa-search mr-1"></i>演示溯源查询
        </button>
    </div>
</script>

<!-- 留样记录管理步骤 -->
<script nonce="{{ csp_nonce }}" type="text/template" id="food_samplesStepTemplate">
    <div class="text-center mb-3">
        <i class="fas fa-vial fa-2x text-success mb-2"></i>
        <h4 class="mb-1">留样记录管理</h4>
        <p class="text-muted small mb-0">一键生成留样记录，确保食品安全，符合法规要求</p>
    </div>

    <div class="row">
        <!-- 功能介绍区域 -->
        <div class="col-md-8">
            <div class="row">
                <div class="col-md-6 mb-3">
                    <div class="card border-success">
                        <div class="card-header bg-success text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-clipboard-list mr-1"></i>留样管理</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>自动生成留样记录</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>留样时间提醒</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>保存期限管理</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>照片证据上传</li>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-3">
                    <div class="card border-primary">
                        <div class="card-header bg-primary text-white py-2">
                            <h6 class="mb-0" style="font-size: 0.9rem;"><i class="fas fa-shield-alt mr-1"></i>法规合规</h6>
                        </div>
                        <div class="card-body py-2">
                            <ul class="list-unstyled mb-0" style="font-size: 0.85rem;">
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>符合食品安全法</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>标准留样流程</li>
                                <li class="mb-1"><i class="fas fa-check text-success mr-2"></i>检查备案支持</li>
                                <li class="mb-0"><i class="fas fa-check text-success mr-2"></i>责任追溯依据</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 视频演示区域 -->
        <div class="col-md-4">
            <div class="card border-success h-100">
                <div class="card-header bg-success text-white text-center py-2">
                    <h6 class="mb-0" style="font-size: 0.9rem;">
                        <i class="fas fa-play-circle mr-1"></i>操作演示
                    </h6>
                </div>
                <div class="card-body p-2" id="videoList_food_samples">
                    <div class="text-center video-loading">
                        <div class="spinner-border spinner-border-sm text-success" role="status">
                            <span class="sr-only">加载中...</span>
                        </div>
                        <p class="mt-2 mb-0 text-muted small">正在加载演示视频...</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="text-center mt-3">
        <a href="{{ url_for('food_samples.index') }}" class="btn btn-success btn-sm mr-2" target="_blank">
            <i class="fas fa-external-link-alt mr-1"></i>留样记录管理
        </a>
        <button class="btn btn-outline-success btn-sm" data-onclick="generateFoodSamples()">
            <i class="fas fa-magic mr-1"></i>一键生成留样
        </button>
    </div>
</script>

<style nonce="{{ csp_nonce }}">
.timeline-sm {
    position: relative;
    padding-left: 20px;
}

.timeline-item {
    position: relative;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
}

.timeline-marker {
    position: absolute;
    left: -25px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
    box-shadow: 0 0 0 2px #dee2e6;
}

.timeline-content {
    margin-left: 10px;
    font-size: 0.9rem;
}

.timeline-item:not(:last-child)::before {
    content: '';
    position: absolute;
    left: -20px;
    top: 12px;
    width: 2px;
    height: 20px;
    background-color: #dee2e6;
}

/* 视频相关样式 */
.video-thumbnail {
    position: relative;
    overflow: hidden;
    border-radius: 0.25rem;
}

.video-thumbnail:hover .video-overlay {
    opacity: 1;
}

.video-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.video-thumbnail:hover {
    transform: scale(1.02);
    transition: transform 0.3s ease;
}

/* 视频模态框样式 */
#videoModal .modal-dialog {
    max-width: 90%;
}

#videoModal video {
    width: 100%;
    height: auto;
    max-height: 70vh;
}

/* 视频加载动画 */
.video-loading {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 120px;
    padding: 20px;
}

/* 视频卡片悬停效果 */
.card:hover {
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: box-shadow 0.3s ease;
}

/* 紧凑按钮样式 */
.btn-xs {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    line-height: 1.2;
    border-radius: 0.2rem;
}

/* 视频列表滚动条样式 */
.video-list-scroll::-webkit-scrollbar {
    width: 4px;
}

.video-list-scroll::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 2px;
}

.video-list-scroll::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 2px;
}

.video-list-scroll::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* 引导页面专用的紧凑布局 */
.guide-video-container {
    max-height: 280px;
    overflow-y: auto;
}

.guide-video-item {
    transition: background-color 0.2s ease;
}

.guide-video-item:hover {
    background-color: rgba(0,0,0,0.02);
    border-radius: 0.25rem;
}

/* 时间轴样式优化 */
.timeline-sm .timeline-marker {
    background-color: #fff;
}

/* 收缩卡片样式 */
.collapse-card-header {
    transition: background-color 0.3s ease;
}

.collapse-card-header:hover {
    background-color: rgba(0,0,0,0.05) !important;
}

.collapse-card-header[aria-expanded="true"] .fas.fa-chevron-down {
    transform: rotate(180deg);
    transition: transform 0.3s ease;
}

.collapse-card-header[aria-expanded="false"] .fas.fa-chevron-down {
    transform: rotate(0deg);
    transition: transform 0.3s ease;
}

/* 视频列表优化样式 */
.video-item {
    border: 1px solid #e9ecef;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
    padding: 0.5rem;
    cursor: pointer;
    transition: all 0.2s ease;
}

.video-item:hover {
    border-color: #007bff;
    background-color: rgba(0,123,255,0.05);
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.video-item:last-child {
    margin-bottom: 0;
}

.video-item .video-title {
    font-size: 0.85rem;
    font-weight: 500;
    color: #495057;
    margin-bottom: 0.25rem;
}

.video-item .video-duration {
    font-size: 0.75rem;
    color: #6c757d;
}

.video-item .play-icon {
    color: #007bff;
    font-size: 1.2rem;
}

/* 引导页面专用的紧凑视频列表 */
.guide-video-list {
    max-height: 250px;
    overflow-y: auto;
    padding-right: 0.25rem;
}

.guide-video-list::-webkit-scrollbar {
    width: 4px;
}

.guide-video-list::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 2px;
}

.guide-video-list::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 2px;
}

.guide-video-list::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}
    border: 2px solid #dee2e6;
    box-shadow: 0 0 0 2px #fff, 0 0 0 4px #dee2e6;
}
</style>

<script nonce="{{ csp_nonce }}" src="{{ url_for('static', filename='js/universal-event-handler.js') }}"></script>