2025-06-04 17:33:15,229 INFO: 保存周菜单成功(主表和副表): id=41 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:500]
2025-06-04 17:33:15,229 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 17:44:07,516 WARNING: Suspicious User-Agent blocked: 'mozilla/5.0 (compatible; genomecrawlerd/1.0; +https://www.nokia.com/genomecrawler)' from *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:103]
2025-06-04 17:44:07,526 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 17:46:54,976 WARNING: Blocked IP attempted access: *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:86]
2025-06-04 17:46:54,992 ERROR: Security middleware error: 403 Forbidden: You don't have the permission to access the requested resource. It is either read-protected or not readable by the server. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:07:02,838 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 18:07:02,838 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 18:07:21,987 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 18:07:27,459 WARNING: Suspicious path blocked: /admin/users from *************** [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:110]
2025-06-04 18:07:27,459 ERROR: Security middleware error: 404 Not Found: The requested URL was not found on the server. If you entered the URL manually please check your spelling and try again. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\security_config.py:161]
2025-06-04 18:07:50,793 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-04 18:07:50,808 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-04 18:08:32,458 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-09"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 18:08:32,458 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 18:08:32,458 INFO: 检查用户权限: user_id=36, area_id=44 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-04 18:08:32,473 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 18:08:32,473 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 18:08:32,473 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 18:08:32,473 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-04 18:08:32,473 INFO: 转换后的日期对象: 2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-04 18:08:32,473 INFO: 计算的周结束日期: 2025-06-15 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-04 18:08:32,473 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-04 18:08:32,473 INFO: 获取周菜单: area_id=44, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
2025-06-04 18:08:32,473 INFO: 使用优化后的查询: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:65]
2025-06-04 18:08:32,473 INFO: 执行主SQL查询: area_id=44, week_start_str=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:77]
2025-06-04 18:08:32,473 INFO: 主查询未找到菜单: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:102]
2025-06-04 18:08:32,473 INFO: 使用日期字符串: week_start_str=2025-06-09, week_end_str=2025-06-15 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:214]
2025-06-04 18:08:32,473 INFO: 准备执行SQL创建菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:217]
2025-06-04 18:08:32,473 INFO: SQL参数: {'area_id': '44', 'week_start_str': '2025-06-09', 'week_end_str': '2025-06-15', 'status': '计划中', 'created_by': 36} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:232]
2025-06-04 18:08:32,473 INFO: 执行SQL: 
            INSERT INTO weekly_menus (area_id, week_start, week_end, status, created_by)
            OUTPUT inserted.id
            VALUES (:area_id, CONVERT(DATETIME2(0), :week_start_str), CONVERT(DATETIME2(0), :week_end_str), :status, :created_by)
             [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:236]
2025-06-04 18:08:32,473 INFO: SQL执行成功，获取到ID: 42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:243]
2025-06-04 18:08:32,473 INFO: 检查数据库连接状态 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:248]
2025-06-04 18:08:32,473 INFO: 数据库连接正常 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:251]
2025-06-04 18:08:32,473 INFO: 事务提交成功 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:258]
2025-06-04 18:08:32,473 INFO: 菜单缓存已清理 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:20]
2025-06-04 18:08:32,473 INFO: 验证成功: 菜单已创建 ID=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:266]
2025-06-04 18:08:32,473 INFO: 周菜单创建成功: id=42 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:125]
2025-06-04 18:08:32,473 INFO: 返回创建周菜单响应: {'success': True, 'message': '周菜单创建成功', 'weekly_menu_id': 42, 'status': '计划中'} [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:142]
2025-06-04 18:08:40,324 INFO: 收到创建周菜单请求: b'{"area_id":"44","week_start":"2025-06-09"}' [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:76]
2025-06-04 18:08:40,324 INFO: 创建周菜单参数: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:87]
2025-06-04 18:08:40,324 INFO: 检查用户权限: user_id=36, area_id=44 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:98]
2025-06-04 18:08:40,324 INFO: 用户角色: ['学校管理员'] [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:99]
2025-06-04 18:08:40,324 INFO: 权限检查结果: is_admin=0, is_school_admin=1, can_access_area=0, has_edit_permission=1 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:106]
2025-06-04 18:08:40,324 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\api_weekly_menu.py:123]
2025-06-04 18:08:40,324 INFO: 开始创建周菜单: area_id=44, week_start=2025-06-09, created_by=36 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:184]
2025-06-04 18:08:40,324 INFO: 转换后的日期对象: 2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:190]
2025-06-04 18:08:40,324 INFO: 计算的周结束日期: 2025-06-15 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:202]
2025-06-04 18:08:40,324 INFO: 检查是否已存在该周的菜单: area_id=44, week_start=2025-06-09 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:205]
2025-06-04 18:08:40,324 INFO: 获取周菜单: area_id=44, week_start=2025-06-09, 类型=<class 'datetime.date'> [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\services\weekly_menu_service.py:42]
