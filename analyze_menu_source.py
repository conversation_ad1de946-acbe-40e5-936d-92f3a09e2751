#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import User, AdministrativeArea, WeeklyMenu, WeeklyMenuRecipe
from datetime import date, datetime
from sqlalchemy import text
import json

def analyze_menu_source():
    """分析城南小学日常管理页面菜单数据来源"""
    app = create_app()
    
    with app.app_context():
        print("=== 分析城南小学日常管理页面菜单数据来源 ===")
        
        # 1. 获取城南小学信息
        area = AdministrativeArea.query.filter_by(name='城南小学').first()
        if not area:
            print("❌ 城南小学不存在")
            return
            
        print(f"学校: {area.name} (ID: {area.id})")
        
        # 2. 分析日期：2025-06-06
        target_date = date(2025, 6, 6)
        day_of_week = target_date.weekday() + 1  # Python的weekday()返回0-6，数据库中是1-7
        
        print(f"目标日期: {target_date} (周{['一', '二', '三', '四', '五', '六', '日'][target_date.weekday()]})")
        print(f"数据库中的day_of_week: {day_of_week}")
        
        # 3. 查询城南小学的所有周菜单
        print(f"\n=== 城南小学的所有周菜单 ===")
        weekly_menus = WeeklyMenu.query.filter_by(area_id=area.id).order_by(WeeklyMenu.created_at.desc()).all()
        
        if not weekly_menus:
            print("❌ 城南小学没有任何周菜单")
            return
            
        print(f"找到 {len(weekly_menus)} 个周菜单:")
        for wm in weekly_menus:
            print(f"  ID: {wm.id}, 周期: {wm.week_start} ~ {wm.week_end}, 状态: {wm.status}")
            
            # 检查是否包含目标日期
            if wm.week_start <= target_date <= wm.week_end:
                print(f"    ✅ 包含目标日期 {target_date}")
                
                # 查询该周菜单的菜谱
                recipes = WeeklyMenuRecipe.query.filter_by(
                    weekly_menu_id=wm.id,
                    day_of_week=day_of_week
                ).all()
                
                if recipes:
                    print(f"    📋 {target_date} 的菜谱:")
                    for recipe in recipes:
                        print(f"      {recipe.meal_type}: {recipe.recipe_name}")
                else:
                    print(f"    ❌ {target_date} 没有菜谱")
            else:
                print(f"    ❌ 不包含目标日期 {target_date}")
        
        # 4. 模拟日常管理页面的查询逻辑
        print(f"\n=== 模拟日常管理页面查询逻辑 ===")
        
        # 这是edit_log路由中使用的SQL
        menu_sql = text("""
        SELECT wmr.meal_type, wmr.recipe_name
        FROM weekly_menu_recipes wmr
        INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
        WHERE wmr.day_of_week = :day_of_week
        AND wm.area_id = :area_id
        AND wm.status = '已发布'
        AND wm.week_start <= :log_date
        AND wm.week_end >= :log_date
        ORDER BY wm.created_at DESC, wmr.meal_type, wmr.id
        """)
        
        try:
            menu_result = db.session.execute(menu_sql, {
                'day_of_week': day_of_week,
                'area_id': area.id,
                'log_date': target_date.strftime('%Y-%m-%d')
            })
            
            # 组织菜谱数据
            menu_data = {
                '早餐': [],
                '午餐': [],
                '晚餐': []
            }
            
            found_recipes = False
            for row in menu_result:
                if row.meal_type in menu_data:
                    menu_data[row.meal_type].append(row.recipe_name)
                    found_recipes = True
                    print(f"  查询到: {row.meal_type} - {row.recipe_name}")
            
            if not found_recipes:
                print("  ❌ SQL查询没有返回任何菜谱")
            else:
                print(f"\n📋 最终菜单数据:")
                for meal_type, recipes in menu_data.items():
                    if recipes:
                        print(f"  {meal_type}: {', '.join(recipes)}")
                    else:
                        print(f"  {meal_type}: 无菜谱")
                        
        except Exception as e:
            print(f"❌ SQL查询失败: {str(e)}")
        
        # 5. 检查具体的筛选条件
        print(f"\n=== 检查筛选条件 ===")
        
        # 检查状态为'已发布'的周菜单
        published_menus = WeeklyMenu.query.filter_by(
            area_id=area.id,
            status='已发布'
        ).all()
        
        print(f"城南小学状态为'已发布'的周菜单: {len(published_menus)} 个")
        for wm in published_menus:
            print(f"  ID: {wm.id}, 周期: {wm.week_start} ~ {wm.week_end}")
            
            # 检查日期范围
            if wm.week_start <= target_date <= wm.week_end:
                print(f"    ✅ 包含目标日期")
                
                # 检查该日期的菜谱
                day_recipes = WeeklyMenuRecipe.query.filter_by(
                    weekly_menu_id=wm.id,
                    day_of_week=day_of_week
                ).all()
                
                print(f"    该日菜谱数量: {len(day_recipes)}")
                for recipe in day_recipes:
                    print(f"      {recipe.meal_type}: {recipe.recipe_name}")
            else:
                print(f"    ❌ 不包含目标日期")
        
        # 6. 总结分析
        print(f"\n=== 总结分析 ===")
        print(f"页面URL: http://127.0.0.1:8080/daily-management/logs/edit/2025-06-06")
        print(f"数据来源: weekly_menus + weekly_menu_recipes 表")
        print(f"查询条件:")
        print(f"  - 学校ID: {area.id} (城南小学)")
        print(f"  - 日期: {target_date}")
        print(f"  - 星期: {day_of_week} (周{['一', '二', '三', '四', '五', '六', '日'][target_date.weekday()]})")
        print(f"  - 状态: 已发布")
        print(f"  - 日期范围: week_start <= {target_date} <= week_end")

if __name__ == '__main__':
    analyze_menu_source()
