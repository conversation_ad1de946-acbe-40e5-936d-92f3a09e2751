#!/usr/bin/env python3
"""
查询城南小学周菜单的详细数据
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app import create_app, db
from app.models import WeeklyMenu, WeeklyMenuRecipe
from datetime import date, datetime
from sqlalchemy import text

def check_weekly_menu_details():
    app = create_app()
    
    with app.app_context():
        print("=== 城南小学周菜单详细数据 ===\n")
        
        area_id = 22  # 城南小学的ID
        today = date.today()
        day_of_week = today.weekday() + 1  # 1=周一, 7=周日
        
        weekday_names = {1: '周一', 2: '周二', 3: '周三', 4: '周四', 5: '周五', 6: '周六', 7: '周日'}
        print(f"今天是: {today} ({weekday_names[day_of_week]})")
        print(f"day_of_week = {day_of_week}\n")
        
        # 查看当前周的菜单
        current_weekly_menu = WeeklyMenu.query.filter(
            WeeklyMenu.area_id == area_id,
            WeeklyMenu.week_start <= today,
            WeeklyMenu.week_end >= today
        ).first()
        
        if current_weekly_menu:
            print(f"当前周菜单: ID={current_weekly_menu.id}")
            print(f"周期: {current_weekly_menu.week_start} ~ {current_weekly_menu.week_end}")
            print(f"状态: {current_weekly_menu.status}\n")
            
            # 查看这周所有的菜谱
            print("本周所有菜谱:")
            all_recipes = WeeklyMenuRecipe.query.filter_by(
                weekly_menu_id=current_weekly_menu.id
            ).order_by(WeeklyMenuRecipe.day_of_week, WeeklyMenuRecipe.meal_type).all()
            
            current_day = None
            for recipe in all_recipes:
                if recipe.day_of_week != current_day:
                    current_day = recipe.day_of_week
                    day_name = weekday_names.get(recipe.day_of_week, f'第{recipe.day_of_week}天')
                    print(f"\n  {day_name} (day_of_week={recipe.day_of_week}):")
                
                print(f"    {recipe.meal_type}: {recipe.recipe_name}")
            
            # 特别查看今天的菜谱
            print(f"\n今天 ({weekday_names[day_of_week]}) 的菜谱:")
            today_recipes = WeeklyMenuRecipe.query.filter_by(
                weekly_menu_id=current_weekly_menu.id,
                day_of_week=day_of_week
            ).all()
            
            if today_recipes:
                for recipe in today_recipes:
                    print(f"  {recipe.meal_type}: {recipe.recipe_name}")
            else:
                print("  今天没有菜谱数据")
                
                # 检查是否有其他day_of_week的数据
                print(f"\n  检查所有day_of_week值:")
                unique_days = db.session.query(WeeklyMenuRecipe.day_of_week).filter_by(
                    weekly_menu_id=current_weekly_menu.id
                ).distinct().all()
                
                for day_tuple in unique_days:
                    day_num = day_tuple[0]
                    count = WeeklyMenuRecipe.query.filter_by(
                        weekly_menu_id=current_weekly_menu.id,
                        day_of_week=day_num
                    ).count()
                    day_name = weekday_names.get(day_num, f'第{day_num}天')
                    print(f"    day_of_week={day_num} ({day_name}): {count} 条菜谱")
        
        # 使用原始SQL查询验证
        print(f"\n=== 使用原始SQL查询验证 ===")
        sql = text("""
        SELECT wmr.day_of_week, wmr.meal_type, wmr.recipe_name
        FROM weekly_menu_recipes wmr
        INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
        WHERE wm.area_id = :area_id
        AND wm.week_start <= :today
        AND wm.week_end >= :today
        ORDER BY wmr.day_of_week, wmr.meal_type
        """)
        
        result = db.session.execute(sql, {
            'area_id': area_id,
            'today': today
        })
        
        print("SQL查询结果:")
        for row in result:
            day_name = weekday_names.get(row.day_of_week, f'第{row.day_of_week}天')
            print(f"  {day_name} {row.meal_type}: {row.recipe_name}")
        
        # 特别查询今天的数据
        print(f"\n今天 (day_of_week={day_of_week}) 的SQL查询:")
        today_sql = text("""
        SELECT wmr.meal_type, wmr.recipe_name
        FROM weekly_menu_recipes wmr
        INNER JOIN weekly_menus wm ON wmr.weekly_menu_id = wm.id
        WHERE wmr.day_of_week = :day_of_week
        AND wm.area_id = :area_id
        AND wm.status IN ('已发布', '计划中')
        ORDER BY wmr.meal_type
        """)
        
        today_result = db.session.execute(today_sql, {
            'day_of_week': day_of_week,
            'area_id': area_id
        })
        
        today_recipes_sql = list(today_result)
        if today_recipes_sql:
            for row in today_recipes_sql:
                print(f"  {row.meal_type}: {row.recipe_name}")
        else:
            print("  SQL查询也没有找到今天的菜谱")

if __name__ == '__main__':
    check_weekly_menu_details()
