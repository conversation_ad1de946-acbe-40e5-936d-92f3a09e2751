2025-06-06 22:06:15,838 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 22:06:19,240 INFO: 生成固定二维码 - 用户: 13974081099, 学校: 城南小学 (ID: 22) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:31]
2025-06-06 22:06:19,240 INFO: 员工上传URL: http://127.0.0.1:8080/daily-management/public/inspections/select-date/22/upload [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:38]
2025-06-06 22:06:19,251 INFO: 成功生成二维码base64，数据长度: 1240 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-06 22:06:19,251 INFO: 管理员评分URL: http://127.0.0.1:8080/daily-management/public/inspections/select-date/22/rate [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\daily_management\inspection_qrcode.py:46]
2025-06-06 22:06:19,261 INFO: 成功生成二维码base64，数据长度: 1072 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\qrcode_helper.py:78]
2025-06-06 22:06:35,278 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-06 22:06:50,283 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-06 22:06:55,521 WARNING: 无法解析日期时间字符串:  [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\utils\datetime_helper.py:297]
2025-06-06 22:09:23,522 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 22:09:23,532 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 22:09:23,534 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 22:09:23,554 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 22:13:58,934 INFO: 查询菜谱：日期=2025-06-06, 星期=4(0=周一), day_of_week=5, 餐次=午餐, 区域ID=22 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:324]
2025-06-06 22:13:58,935 INFO: 找到 0 个周菜单 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:334]
2025-06-06 22:13:58,937 INFO: 食材一致性分析完成: 匹配率=0%, 缺失=0, 多余=0 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\food_trace.py:553]
2025-06-06 22:14:38,105 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 22:14:38,114 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 22:14:38,116 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 22:14:38,137 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 22:17:04,501 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 22:17:04,510 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
2025-06-06 22:17:04,512 ERROR: 获取供应链状态失败: (pyodbc.Error) ('HYC00', '[HYC00] [Microsoft][ODBC SQL Server Driver]没有执行可选特性 (0) (SQLBindParameter)')
[SQL: 
            SELECT
                COUNT(CASE WHEN CAST(created_at AS DATE) = ? THEN 1 END) as today_batches,
                COUNT(CASE WHEN status = '待检验' THEN 1 END) as pending_inspection,
                CASE
                    WHEN COUNT(*) > 0 THEN
                        CAST(COUNT(CASE WHEN quality_status = '合格' THEN 1 END) * 100.0 / COUNT(*) AS DECIMAL(5,1))
                    ELSE 0
                END as quality_rate
            FROM stock_in_records
            WHERE area_id = ?
            AND created_at >= DATEADD(day, -7, GETDATE())
        ]
[parameters: (datetime.date(2025, 6, 6), 22)]
(Background on this error at: https://sqlalche.me/e/20/dbapi) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:303]
2025-06-06 22:17:04,533 ERROR: 获取通知消息失败: (pyodbc.ProgrammingError) ('42000', '[42000] [Microsoft][ODBC SQL Server Driver][SQL Server]“LIMIT”附近有语法错误。 (102) (SQLExecDirectW); [42000] [Microsoft][ODBC SQL Server Driver][SQL Server]无法预定义语句。 (8180)')
[SQL: 
            SELECT i.ingredient_name, i.quantity, i.unit, i.min_stock_level
            FROM inventory i
            INNER JOIN warehouses w ON i.warehouse_id = w.id
            WHERE w.area_id = ?
            AND i.status = '正常'
            AND i.quantity <= i.min_stock_level
            AND i.min_stock_level > 0
            ORDER BY (i.quantity / NULLIF(i.min_stock_level, 0)) ASC
            LIMIT 3
        ]
[parameters: (22,)]
(Background on this error at: https://sqlalche.me/e/20/f405) [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:424]
2025-06-06 22:17:18,231 INFO: 开始查询最近 5 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:57]
2025-06-06 22:17:18,244 INFO: 成功获取 0 条陪餐记录 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\routes\dashboard_api.py:91]
