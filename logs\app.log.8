2025-06-04 20:45:20,336 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:45:23,142 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:47:55,350 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:48:21,170 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:48:25,926 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:50:44,884 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:50:45,601 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:53:45,710 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 20:57:15,929 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:57:19,461 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 20:59:38,225 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:00:07,871 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:00:09,142 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:03:06,525 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:03:11,974 INFO: 更新视频请求参数: step_name=weekly_menu, new_video_name=菜单计划创建, original_video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:318]
2025-06-04 21:04:11,282 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:04:36,078 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:316]
2025-06-04 21:04:39,533 ERROR: 更新视频失败: 400 Bad Request: The browser (or proxy) sent a request that this server could not understand. [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:337]
2025-06-04 21:05:26,615 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:316]
2025-06-04 21:05:26,772 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:316]
2025-06-04 21:13:46,122 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:14:18,805 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:15:01,428 INFO: 视频上传成功: 步骤=weekly_menu, 文件=20250604211501_15c71927_WEEEK.mp4, 大小=9786990字节 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:216]
2025-06-04 21:15:05,434 INFO: 视频上传成功: 步骤=weekly_menu, 文件=20250604211505_ec805c70_WEEEK.mp4, 大小=9786990字节 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:216]
2025-06-04 21:24:23,657 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:24:58,333 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:24:58,345 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604212458_df978692_WEEEK.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:24:59,885 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:24:59,901 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604212459_92e8652f_WEEEK.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:26:11,437 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:26:11,448 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604212611_ba2ebceb_WEEEK.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:26:15,291 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:26:15,302 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604212615_870649ed_WEEEK.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:29:21,496 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
2025-06-04 21:29:59,350 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:29:59,362 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604212959_e9232888_WEEEK.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:30:06,237 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:30:06,250 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604213006_3ab38484_WEEEK.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:46:55,713 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:46:55,717 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604214655_8173c522_bandicam_2025-06-01_20-51-43-787.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:46:56,376 INFO: 更新视频信息: step_name=weekly_menu, video_name=菜单计划创建, description= [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:315]
2025-06-04 21:46:56,380 INFO: 视频文件已更新: C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\static\videos\weekly_menu\20250604214656_48c7c780_bandicam_2025-06-01_20-51-43-787.mp4 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\admin\guide_management_routes.py:373]
2025-06-04 21:47:47,563 INFO: 应用启动 [in C:\Users\<USER>\Documents\augment-projects\StudentsCMSSP\app\__init__.py:789]
